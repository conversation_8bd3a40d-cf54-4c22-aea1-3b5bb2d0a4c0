#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取器单元测试

测试特征提取器类的基本功能。
"""

import numpy as np
from PIL import Image
from unittest.mock import Mock, patch

from features.core.feature_extractor import FeatureExtractor
from features.config.feature_config import FeatureExtractorConfig


class TestFeatureExtractor:
    """特征提取器测试类"""

    def test_init(self):
        """测试特征提取器初始化"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        assert extractor is not None
        assert extractor.config == config

    def test_basic_functionality(self):
        """测试基本功能"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        assert extractor is not None
        assert hasattr(extractor, 'config')
        assert hasattr(extractor, 'deep_extractor')
        assert hasattr(extractor, 'traditional_extractor')

    def test_extract_invalid_image_path(self):
        """测试无效图像路径处理"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        
        # 测试不存在的图像路径
        result = extractor.extract_features('/nonexistent/image.jpg')
        assert result is not None
        assert result.success is False

    def test_cache_functionality(self, temp_dir):
        """测试缓存功能"""
        config = FeatureExtractorConfig()
        config.use_cache = True
        config.cache_dir = str(temp_dir)
        
        extractor = FeatureExtractor(config)
        assert extractor.cache_manager is not None

    def test_config_validation(self):
        """测试配置验证"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        assert extractor is not None

    def test_extract_features_from_image(self):
        """测试从图像对象提取特征"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # 模拟深度特征提取
        with patch.object(extractor.deep_extractor, 'extract_features') as mock_deep:
            mock_deep.return_value = np.array([1, 2, 3, 4])
            
            features = extractor.extract_features_from_image(test_image)
            assert isinstance(features, dict)

    def test_change_model(self):
        """测试模型切换"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        
        # 模拟模型切换
        with patch.object(extractor.deep_extractor, 'change_model') as mock_change:
            extractor.change_model('resnet101')
            mock_change.assert_called_once_with('resnet101')

    def test_cleanup(self):
        """测试清理功能"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        
        # 模拟清理
        with patch.object(extractor.deep_extractor, 'cleanup') as mock_cleanup:
            extractor.cleanup()
            mock_cleanup.assert_called_once()

    def test_traditional_features(self):
        """测试传统特征提取"""
        config = FeatureExtractorConfig()
        extractor = FeatureExtractor(config)
        
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # 模拟传统特征提取
        with patch.object(extractor.traditional_extractor, 'extract_features') as mock_traditional:
            mock_traditional.return_value = {
                'color': np.array([1, 2, 3]),
                'texture': np.array([4, 5, 6])
            }
            
            features = extractor.extract_features_from_image(test_image, extract_traditional=True)
            assert isinstance(features, dict) 