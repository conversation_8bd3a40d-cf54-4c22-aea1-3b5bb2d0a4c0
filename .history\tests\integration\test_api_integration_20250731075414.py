"""
API集成测试
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest
from PIL import Image
import numpy as np


class TestAPIIntegration:
    """API集成测试类"""

    def test_health_check(self, client):
        """测试健康检查端点"""
        response = client.get('/api/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'version' in data

    def test_search_endpoint(self, client, test_images_dir):
        """测试搜索端点"""
        # 创建测试图像文件
        test_image_path = Path(test_images_dir) / "test_query.jpg"
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(test_image_path)
        
        # 测试图像搜索
        with open(test_image_path, 'rb') as f:
            response = client.post(
                '/api/search',
                data={'image': (f, 'test_query.jpg')},
                content_type='multipart/form-data'
            )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'results' in data
        assert 'total_count' in data
        assert 'search_time' in data

    def test_search_with_parameters(self, client, test_images_dir):
        """测试带参数的搜索"""
        # 创建测试图像
        test_image_path = Path(test_images_dir) / "test_query.jpg"
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(test_image_path)
        
        # 测试带参数的搜索
        with open(test_image_path, 'rb') as f:
            response = client.post(
                '/api/search',
                data={
                    'image': (f, 'test_query.jpg'),
                    'top_k': '5',
                    'similarity_threshold': '0.8'
                },
                content_type='multipart/form-data'
            )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['results']) <= 5

    def test_batch_search_endpoint(self, client, test_images_dir):
        """测试批量搜索端点"""
        # 创建多个测试图像
        test_images = []
        for i in range(3):
            image_path = Path(test_images_dir) / f"test_batch_{i}.jpg"
            img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
            img.save(image_path)
            test_images.append(image_path)
        
        # 测试批量搜索
        files = []
        for image_path in test_images:
            files.append(('images', (open(image_path, 'rb'), image_path.name)))
        
        response = client.post(
            '/api/batch_search',
            data=files,
            content_type='multipart/form-data'
        )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['results']) == 3
        
        # 关闭文件
        for _, (f, _) in files:
            f.close()

    def test_feature_extraction_endpoint(self, client, test_images_dir):
        """测试特征提取端点"""
        # 创建测试图像
        test_image_path = Path(test_images_dir) / "test_extract.jpg"
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(test_image_path)
        
        # 测试特征提取
        with open(test_image_path, 'rb') as f:
            response = client.post(
                '/api/extract_features',
                data={'image': (f, 'test_extract.jpg')},
                content_type='multipart/form-data'
            )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'features' in data
        assert 'color' in data['features']
        assert 'texture' in data['features']

    def test_index_management_endpoints(self, client):
        """测试索引管理端点"""
        # 测试获取索引统计
        response = client.get('/api/index/stats')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'total_images' in data
        assert 'index_size' in data

    def test_error_handling(self, client):
        """测试错误处理"""
        # 测试无效的搜索请求
        response = client.post('/api/search', data={})
        assert response.status_code == 400
        
        # 测试无效的图像文件
        response = client.post(
            '/api/search',
            data={'image': (b'invalid', 'test.txt')},
            content_type='multipart/form-data'
        )
        assert response.status_code == 400

    def test_search_with_filters(self, client, test_images_dir):
        """测试带过滤器的搜索"""
        # 创建测试图像
        test_image_path = Path(test_images_dir) / "test_filter.jpg"
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(test_image_path)
        
        # 测试带过滤器的搜索
        with open(test_image_path, 'rb') as f:
            response = client.post(
                '/api/search',
                data={
                    'image': (f, 'test_filter.jpg'),
                    'filters': json.dumps({'category': 'fabric'})
                },
                content_type='multipart/form-data'
            )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'results' in data

    def test_api_rate_limiting(self, client, test_images_dir):
        """测试API速率限制"""
        # 创建测试图像
        test_image_path = Path(test_images_dir) / "test_rate_limit.jpg"
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(test_image_path)
        
        # 快速发送多个请求
        responses = []
        for _ in range(10):
            with open(test_image_path, 'rb') as f:
                response = client.post(
                    '/api/search',
                    data={'image': (f, 'test_rate_limit.jpg')},
                    content_type='multipart/form-data'
                )
                responses.append(response.status_code)
        
        # 验证大部分请求成功
        success_count = sum(1 for code in responses if code == 200)
        assert success_count >= 8  # 至少80%的请求成功

    def test_api_authentication(self, client):
        """测试API认证"""
        # 测试需要认证的端点
        response = client.get('/api/admin/stats')
        assert response.status_code in [401, 403]  # 未认证或未授权

    def test_api_documentation(self, client):
        """测试API文档"""
        # 测试Swagger文档
        response = client.get('/api/docs')
        assert response.status_code == 200

    def test_cors_headers(self, client):
        """测试CORS头"""
        response = client.get('/api/health')
        assert 'Access-Control-Allow-Origin' in response.headers

    def test_search_response_format(self, client, test_images_dir):
        """测试搜索响应格式"""
        # 创建测试图像
        test_image_path = Path(test_images_dir) / "test_format.jpg"
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(test_image_path)
        
        # 执行搜索
        with open(test_image_path, 'rb') as f:
            response = client.post(
                '/api/search',
                data={'image': (f, 'test_format.jpg')},
                content_type='multipart/form-data'
            )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        
        # 验证响应格式
        required_fields = ['results', 'total_count', 'search_time', 'query_info']
        for field in required_fields:
            assert field in data
        
        # 验证结果格式
        if data['results']:
            result = data['results'][0]
            assert 'image_path' in result
            assert 'similarity' in result
            assert 'metadata' in result

    def test_batch_processing_status(self, client):
        """测试批量处理状态端点"""
        response = client.get('/api/batch/status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'status' in data
        assert 'progress' in data
        assert 'total_tasks' in data

    def test_configuration_endpoints(self, client):
        """测试配置端点"""
        # 测试获取配置
        response = client.get('/api/config')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'search' in data
        assert 'features' in data

    def test_search_history(self, client):
        """测试搜索历史端点"""
        response = client.get('/api/search/history')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'searches' in data
        assert isinstance(data['searches'], list)

    def test_api_versioning(self, client):
        """测试API版本控制"""
        # 测试v1版本
        response = client.get('/api/v1/health')
        assert response.status_code == 200
        
        # 测试默认版本
        response = client.get('/api/health')
        assert response.status_code == 200 