app_name: <PERSON>abric Search
api:
  cors_enabled: true
  cors_origins:
  - '*'
  debug: false
  docs_path: /api/docs
  enable_docs: true
  host: 127.0.0.1
  max_content_length: 16777216
  port: 5000
  request_timeout: 30
  threaded: true
batch_processing:
  batch_size: 32
  chunk_size: 100
  error_log_file: batch_errors.log
  max_errors: 100
  max_workers: 4
  memory_limit: null
  progress_file: batch_progress.json
  progress_update_interval: 1.0
  queue_size: 1000
  retry_attempts: 3
  retry_delay: 1.0
  save_progress: true
  stop_on_error: false
  timeout: 300
  use_multiprocessing: true
cache:
  backend: memory
  cache_dir: cache
  enabled: true
  max_disk_size: 1073741824
  max_size: 1000
  redis_db: 0
  redis_host: localhost
  redis_password: null
  redis_port: 6379
  ttl: 3600
cache_dir: cache
data_dir: data
database:
  database: fabric_search
  echo: false
  host: localhost
  max_overflow: 20
  password: ''
  pool_recycle: 3600
  pool_size: 10
  pool_timeout: 30
  port: 5432
  username: postgres
debug: false
feature_extraction:
  batch_size: 32
  cache_features: true
  center_crop: true
  color_jitter: false
  device: auto
  extract_color: true
  extract_shape: true
  extract_texture: true
  feature_dim: 2048
  hist_bins: 32
  horizontal_flip: false
  image_size:
  - 224
  - 224
  lbp_n_points: 24
  lbp_radius: 3
  model_name: resnet50
  model_path: null
  n_dominant_colors: 5
  n_fourier_descriptors: 32
  normalize: true
  num_workers: 4
  pin_memory: true
  prefetch_factor: 2
  random_crop: false
  resize_method: bilinear
  use_mixed_precision: true
log_dir: logs
logging:
  backup_count: 5
  console_level: INFO
  date_format: '%Y-%m-%d %H:%M:%S'
  error_log: true
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  log_file: fabric_search.log
  log_to_console: true
  log_to_file: true
  max_file_size: 10485760
  performance_log: true
  sql_log: false
model_dir: models
performance:
  allow_memory_growth: true
  enable_profiling: false
  gpu_memory_fraction: 0.8
  max_concurrent_requests: 10
  monitor_system_resources: true
  profile_output_dir: profiles
  request_timeout: 30
  resource_check_interval: 60
search:
  cache_results: true
  cache_size: 1000
  cache_ttl: 3600
  faiss_ef_construction: 200
  faiss_ef_search: 50
  faiss_index_type: IndexFlatIP
  faiss_m: 16
  faiss_nlist: 100
  faiss_nprobe: 10
  index_type: faiss
  similarity_metric: cosine
  similarity_threshold: 0.0
  top_k: 50
  use_gpu: true
security:
  allowed_extensions:
  - .jpg
  - .jpeg
  - .png
  - .bmp
  - .tiff
  allowed_paths: []
  api_key_required: false
  blocked_paths: []
  max_file_size: 10485760
  max_requests_per_minute: 100
  rate_limit_enabled: true
  scan_uploads: true
  secret_key: ''
temp_dir: temp
version: 2.0.0
