#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接管理器

该模块提供数据库连接的创建、配置和管理功能。
"""

import sqlite3
import logging
import threading
from pathlib import Path
from typing import Optional
from contextlib import contextmanager

from ..config.database_config import DatabaseConfig


class ConnectionManager:
    """数据库连接管理器"""
    
    def __init__(self, config: DatabaseConfig):
        """初始化连接管理器
        
        Args:
            config: 数据库配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._local = threading.local()
        self._lock = threading.Lock()
        
        # 确保数据库目录存在
        db_path = Path(config.db_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def get_connection(self) -> sqlite3.Connection:
        """获取线程本地数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接
        """
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            try:
                conn = sqlite3.connect(
                    self.config.db_path,
                    timeout=self.config.timeout,
                    check_same_thread=self.config.check_same_thread,
                    isolation_level=self.config.isolation_level
                )
                
                # 设置行工厂
                conn.row_factory = sqlite3.Row
                
                # 配置数据库
                self._configure_connection(conn)
                
                self._local.connection = conn
                self.logger.debug("数据库连接已建立")
                
            except Exception as e:
                self.logger.error(f"建立数据库连接失败: {e}")
                raise
        
        return self._local.connection
    
    def _configure_connection(self, conn: sqlite3.Connection):
        """配置数据库连接
        
        Args:
            conn: 数据库连接
        """
        cursor = conn.cursor()
        
        try:
            # 启用外键约束
            if self.config.enable_foreign_keys:
                cursor.execute("PRAGMA foreign_keys = ON")
            
            # 设置WAL模式
            if self.config.enable_wal_mode:
                cursor.execute("PRAGMA journal_mode = WAL")
            
            # 设置缓存大小
            cursor.execute(f"PRAGMA cache_size = {self.config.cache_size}")
            
            # 设置同步模式
            cursor.execute(f"PRAGMA synchronous = {self.config.synchronous}")
            
            # 设置临时存储
            cursor.execute("PRAGMA temp_store = MEMORY")
            
            # 设置mmap大小
            cursor.execute("PRAGMA mmap_size = 268435456")  # 256MB
            
            conn.commit()
            
        except Exception as e:
            self.logger.error(f"配置数据库连接失败: {e}")
            raise
        finally:
            cursor.close()
    
    @contextmanager
    def connection_context(self):
        """获取数据库连接上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        conn = self.get_connection()
        try:
            yield conn
        except Exception:
            conn.rollback()
            raise
        finally:
            # 不关闭连接，保持线程本地连接
            pass
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            try:
                self._local.connection.close()
                self._local.connection = None
                self.logger.debug("数据库连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {e}")