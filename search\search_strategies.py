#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索策略模块

该模块实现多种搜索策略，包括加权搜索、自适应搜索和查询扩展搜索。
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from utils.log_utils import LoggerMixin
from features.search.similarity_search import SimilarityCalculator
from database.models import FabricImage


class SearchStrategyType(Enum):
    """搜索策略类型"""
    WEIGHTED = "weighted"  # 加权搜索
    ADAPTIVE = "adaptive"  # 自适应搜索
    QUERY_EXPANSION = "query_expansion"  # 查询扩展搜索
    HYBRID = "hybrid"  # 混合搜索
    SINGLE_FEATURE = "single_feature"  # 单特征搜索


@dataclass
class FeatureWeights:
    """特征权重"""
    color_weight: float = 0.25
    texture_weight: float = 0.25
    shape_weight: float = 0.25
    deep_learning_weight: float = 0.25
    
    def normalize(self):
        """归一化权重"""
        total = (self.color_weight + self.texture_weight + 
                self.shape_weight + self.deep_learning_weight)
        if total > 0:
            self.color_weight /= total
            self.texture_weight /= total
            self.shape_weight /= total
            self.deep_learning_weight /= total
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典"""
        return {
            'color': self.color_weight,
            'texture': self.texture_weight,
            'shape': self.shape_weight,
            'deep_learning': self.deep_learning_weight
        }


@dataclass
class SearchResult:
    """搜索结果"""
    fabric_image: FabricImage
    similarity_score: float
    feature_scores: Dict[str, float]  # 各特征维度的相似度分数
    strategy_used: str
    

class SearchStrategy(ABC, LoggerMixin):
    """搜索策略基类"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator):
        super().__init__()
        self.similarity_calculator = similarity_calculator
    
    @abstractmethod
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行搜索"""
        pass


class WeightedSearchStrategy(SearchStrategy):
    """加权搜索策略"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator,
                 feature_weights: Optional[FeatureWeights] = None):
        super().__init__(similarity_calculator)
        self.feature_weights = feature_weights or FeatureWeights()
        self.feature_weights.normalize()
    
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行加权搜索"""
        
        # 计算各特征维度的相似度
        feature_similarities = {}
        
        for feature_type, query_feature in query_features.items():
            if feature_type in database_features:
                db_features = np.array(database_features[feature_type])
                
                # 计算相似度
                similarity_result = self.similarity_calculator.calculate_similarity(
                    query_feature.reshape(1, -1),
                    db_features,
                    top_k=len(fabric_images)
                )
                
                # 提取相似度分数
                similarities = np.zeros(len(fabric_images))
                for item_id, score in similarity_result.similar_items:
                    if isinstance(item_id, int) and 0 <= item_id < len(fabric_images):
                        similarities[item_id] = score
                
                feature_similarities[feature_type] = similarities
        
        # 加权融合相似度分数
        final_similarities = self._weighted_fusion(feature_similarities)
        
        # 排序并返回结果
        return self._create_search_results(
            final_similarities, feature_similarities, 
            fabric_images, top_k, "weighted"
        )
    
    def _weighted_fusion(self, feature_similarities: Dict[str, np.ndarray]) -> np.ndarray:
        """加权融合相似度分数"""
        weights = self.feature_weights.to_dict()
        
        # 初始化最终相似度
        num_images = len(next(iter(feature_similarities.values())))
        final_similarities = np.zeros(num_images)
        
        # 加权求和
        for feature_type, similarities in feature_similarities.items():
            weight = weights.get(feature_type, 0.0)
            final_similarities += weight * similarities
            
            # 添加调试信息
            max_sim = np.max(similarities) if len(similarities) > 0 else 0
            min_sim = np.min(similarities) if len(similarities) > 0 else 0
            mean_sim = np.mean(similarities) if len(similarities) > 0 else 0
            self.logger.debug(f"特征类型: {feature_type}, 权重: {weight:.3f}, 相似度范围: [{min_sim:.3f}, {max_sim:.3f}], 平均: {mean_sim:.3f}")
        
        # 添加最终相似度的调试信息
        if len(final_similarities) > 0:
            max_final = np.max(final_similarities)
            min_final = np.min(final_similarities)
            mean_final = np.mean(final_similarities)
            self.logger.debug(f"最终相似度范围: [{min_final:.3f}, {max_final:.3f}], 平均: {mean_final:.3f}")
        
        return final_similarities
    
    def _create_search_results(self, 
                             final_similarities: np.ndarray,
                             feature_similarities: Dict[str, np.ndarray],
                             fabric_images: List[FabricImage],
                             top_k: int,
                             strategy_name: str) -> List[SearchResult]:
        """创建搜索结果"""
        # 获取top_k结果的索引
        top_indices = np.argsort(final_similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            # 构建特征分数字典
            feature_scores = {}
            for feature_type, similarities in feature_similarities.items():
                feature_scores[feature_type] = float(similarities[idx])
            
            result = SearchResult(
                fabric_image=fabric_images[idx],
                similarity_score=float(final_similarities[idx]),
                feature_scores=feature_scores,
                strategy_used=strategy_name
            )
            results.append(result)
        
        return results


class AdaptiveSearchStrategy(SearchStrategy):
    """自适应搜索策略"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator):
        super().__init__(similarity_calculator)
    
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行自适应搜索"""
        
        # 分析查询图像特征，自动调整权重
        adaptive_weights = self._analyze_query_features(query_features)
        
        # 使用自适应权重执行加权搜索
        weighted_strategy = WeightedSearchStrategy(
            self.similarity_calculator, adaptive_weights
        )
        
        results = weighted_strategy.search(
            query_features, database_features, fabric_images, top_k, **kwargs
        )
        
        # 更新策略名称
        for result in results:
            result.strategy_used = "adaptive"
        
        return results
    
    def _analyze_query_features(self, query_features: Dict[str, np.ndarray]) -> FeatureWeights:
        """分析查询特征，自动调整权重"""
        weights = FeatureWeights()
        
        # 计算各特征的方差作为重要性指标
        feature_variances = {}
        
        for feature_type, features in query_features.items():
            if len(features) > 0:
                variance = np.var(features)
                feature_variances[feature_type] = variance
        
        # 根据方差调整权重（方差大的特征更重要）
        total_variance = sum(feature_variances.values())
        
        if total_variance > 0:
            if 'color' in feature_variances:
                weights.color_weight = feature_variances['color'] / total_variance
            if 'texture' in feature_variances:
                weights.texture_weight = feature_variances['texture'] / total_variance
            if 'shape' in feature_variances:
                weights.shape_weight = feature_variances['shape'] / total_variance
            if 'deep_learning' in feature_variances:
                weights.deep_learning_weight = feature_variances['deep_learning'] / total_variance
        
        weights.normalize()
        
        self.logger.debug(f"自适应权重: {weights.to_dict()}")
        
        return weights


class QueryExpansionStrategy(SearchStrategy):
    """查询扩展搜索策略"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator,
                 expansion_ratio: float = 0.1):
        super().__init__(similarity_calculator)
        self.expansion_ratio = expansion_ratio  # 扩展比例
    
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行查询扩展搜索"""
        
        # 第一轮搜索：获取初步结果
        initial_k = max(int(top_k * (1 + self.expansion_ratio)), top_k + 5)
        
        weighted_strategy = WeightedSearchStrategy(self.similarity_calculator)
        initial_results = weighted_strategy.search(
            query_features, database_features, fabric_images, initial_k, **kwargs
        )
        
        # 查询扩展：使用初步结果的特征扩展查询
        expanded_features = self._expand_query(query_features, initial_results)
        
        # 第二轮搜索：使用扩展后的查询
        final_results = weighted_strategy.search(
            expanded_features, database_features, fabric_images, top_k, **kwargs
        )
        
        # 更新策略名称
        for result in final_results:
            result.strategy_used = "query_expansion"
        
        return final_results
    
    def _expand_query(self, 
                     original_features: Dict[str, np.ndarray],
                     initial_results: List[SearchResult]) -> Dict[str, np.ndarray]:
        """扩展查询特征"""
        expanded_features = {}
        
        # 选择top相似结果用于扩展
        expansion_count = max(1, int(len(initial_results) * self.expansion_ratio))
        top_results = initial_results[:expansion_count]
        
        for feature_type, original_feature in original_features.items():
            # 收集相似图像的特征
            similar_features = []
            for result in top_results:
                if hasattr(result.fabric_image, 'features') and result.fabric_image.features is not None:
                    # 这里需要根据实际的特征存储格式来提取对应的特征
                    # 假设特征按照某种格式存储
                    similar_features.append(result.fabric_image.features)
            
            if similar_features:
                # 计算扩展特征（原始特征 + 相似特征的加权平均）
                similar_features_array = np.array(similar_features)
                mean_similar_features = np.mean(similar_features_array, axis=0)
                
                # 加权融合（原始特征权重更高）
                alpha = 0.7  # 原始特征权重
                expanded_feature = alpha * original_feature + (1 - alpha) * mean_similar_features
                expanded_features[feature_type] = expanded_feature
            else:
                expanded_features[feature_type] = original_feature
        
        return expanded_features


class HybridSearchStrategy(SearchStrategy):
    """混合搜索策略"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator):
        super().__init__(similarity_calculator)
        
        # 初始化各种策略
        self.weighted_strategy = WeightedSearchStrategy(similarity_calculator)
        self.adaptive_strategy = AdaptiveSearchStrategy(similarity_calculator)
        self.query_expansion_strategy = QueryExpansionStrategy(similarity_calculator)
    
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行混合搜索"""
        
        # 执行多种策略
        weighted_results = self.weighted_strategy.search(
            query_features, database_features, fabric_images, top_k * 2, **kwargs
        )
        
        adaptive_results = self.adaptive_strategy.search(
            query_features, database_features, fabric_images, top_k * 2, **kwargs
        )
        
        query_expansion_results = self.query_expansion_strategy.search(
            query_features, database_features, fabric_images, top_k * 2, **kwargs
        )
        
        # 融合多种策略的结果
        final_results = self._fuse_results(
            [weighted_results, adaptive_results, query_expansion_results],
            top_k
        )
        
        # 更新策略名称
        for result in final_results:
            result.strategy_used = "hybrid"
        
        return final_results
    
    def _fuse_results(self, 
                     strategy_results: List[List[SearchResult]], 
                     top_k: int) -> List[SearchResult]:
        """融合多种策略的结果"""
        # 收集所有结果
        all_results = {}
        
        for strategy_idx, results in enumerate(strategy_results):
            strategy_weight = 1.0 / len(strategy_results)  # 等权重
            
            for rank, result in enumerate(results):
                image_id = result.fabric_image.id if hasattr(result.fabric_image, 'id') else id(result.fabric_image)
                
                if image_id not in all_results:
                    all_results[image_id] = {
                        'result': result,
                        'scores': [],
                        'ranks': []
                    }
                
                # 计算排名分数（排名越靠前分数越高）
                rank_score = 1.0 / (rank + 1)
                all_results[image_id]['scores'].append(result.similarity_score * strategy_weight)
                all_results[image_id]['ranks'].append(rank_score * strategy_weight)
        
        # 计算最终分数
        final_results = []
        for image_id, data in all_results.items():
            # 综合相似度分数和排名分数
            final_score = np.mean(data['scores']) + 0.1 * np.mean(data['ranks'])
            
            result = data['result']
            result.similarity_score = final_score
            final_results.append(result)
        
        # 按最终分数排序
        final_results.sort(key=lambda x: x.similarity_score, reverse=True)
        
        return final_results[:top_k]


class SingleFeatureSearchStrategy(SearchStrategy):
    """单特征搜索策略"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator, feature_type: str):
        super().__init__(similarity_calculator)
        self.feature_type = feature_type
        
        # 验证特征类型
        valid_features = ['color', 'texture', 'shape', 'deep_learning']
        if feature_type not in valid_features:
            raise ValueError(f"不支持的特征类型: {feature_type}，支持的类型: {valid_features}")
    
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行单特征搜索"""
        
        # 检查查询特征是否包含指定特征类型
        if self.feature_type not in query_features:
            self.logger.warning(f"查询特征中不包含 {self.feature_type} 特征")
            return []
        
        # 检查数据库特征是否包含指定特征类型
        if self.feature_type not in database_features:
            self.logger.warning(f"数据库特征中不包含 {self.feature_type} 特征")
            return []
        
        query_feature = query_features[self.feature_type]
        db_features = database_features[self.feature_type]
        
        # 计算相似度
        similarities = []
        for i, db_feature in enumerate(db_features):
            try:
                similarity = self.similarity_calculator.calculate_similarity(
                    query_feature, db_feature, self.feature_type
                )
                similarities.append((i, similarity))
            except Exception as e:
                self.logger.error(f"计算相似度失败 (索引 {i}): {e}")
                similarities.append((i, 0.0))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # 创建搜索结果
        results = []
        for i, (idx, similarity) in enumerate(similarities[:top_k]):
            if idx < len(fabric_images):
                feature_scores = {self.feature_type: similarity}
                result = SearchResult(
                    fabric_image=fabric_images[idx],
                    similarity_score=similarity,
                    feature_scores=feature_scores,
                    strategy_used=f"single_{self.feature_type}"
                )
                results.append(result)
        
        self.logger.info(f"单特征搜索完成，特征类型: {self.feature_type}，返回 {len(results)} 个结果")
        return results


class ExpansionSearchStrategy(SearchStrategy):
    """查询扩展搜索策略（兼容文档接口）"""
    
    def __init__(self, similarity_calculator: SimilarityCalculator,
                 expansion_ratio: float = 0.1):
        super().__init__(similarity_calculator)
        self.expansion_ratio = expansion_ratio
        # 内部使用QueryExpansionStrategy实现
        self._query_expansion_strategy = QueryExpansionStrategy(
            similarity_calculator, expansion_ratio
        )
    
    def search(self, 
              query_features: Dict[str, np.ndarray],
              database_features: Dict[str, List[np.ndarray]],
              fabric_images: List[FabricImage],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """执行查询扩展搜索
        
        Args:
            query_features: 查询特征字典
            database_features: 数据库特征字典
            fabric_images: 布料图像列表
            top_k: 返回结果数量
            **kwargs: 其他参数
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        try:
            # 使用内部的QueryExpansionStrategy实现
            results = self._query_expansion_strategy.search(
                query_features, database_features, fabric_images, top_k, **kwargs
            )
            
            # 更新策略名称
            for result in results:
                result.strategy_used = "expansion"
            
            self.logger.info(f"查询扩展搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"查询扩展搜索失败: {e}")
            return []


class SearchStrategyFactory:
    """搜索策略工厂"""
    
    @staticmethod
    def create_strategy(strategy_type: SearchStrategyType,
                       similarity_calculator: SimilarityCalculator,
                       **kwargs) -> SearchStrategy:
        """创建搜索策略"""
        
        if strategy_type == SearchStrategyType.WEIGHTED:
            feature_weights = kwargs.get('feature_weights')
            return WeightedSearchStrategy(similarity_calculator, feature_weights)
        
        elif strategy_type == SearchStrategyType.ADAPTIVE:
            return AdaptiveSearchStrategy(similarity_calculator)
        
        elif strategy_type == SearchStrategyType.QUERY_EXPANSION:
            expansion_ratio = kwargs.get('expansion_ratio', 0.1)
            return QueryExpansionStrategy(similarity_calculator, expansion_ratio)
        
        elif strategy_type == SearchStrategyType.HYBRID:
            return HybridSearchStrategy(similarity_calculator)
        
        elif strategy_type == SearchStrategyType.SINGLE_FEATURE:
            feature_type = kwargs.get('feature_type')
            if not feature_type:
                raise ValueError("单特征搜索需要指定 feature_type 参数")
            return SingleFeatureSearchStrategy(similarity_calculator, feature_type)
        
        else:
            raise ValueError(f"不支持的搜索策略类型: {strategy_type}")
    
    @staticmethod
    def create_expansion_strategy(similarity_calculator: SimilarityCalculator,
                                expansion_ratio: float = 0.1) -> ExpansionSearchStrategy:
        """创建查询扩展搜索策略（兼容文档接口）
        
        Args:
            similarity_calculator: 相似度计算器
            expansion_ratio: 扩展比例
            
        Returns:
            ExpansionSearchStrategy: 查询扩展搜索策略
        """
        return ExpansionSearchStrategy(similarity_calculator, expansion_ratio)