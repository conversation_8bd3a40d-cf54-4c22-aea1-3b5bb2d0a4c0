"""核心特征管理器

重构后的特征管理器主类，整合所有子模块功能。
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# 导入子模块
from features.storage import FeatureStorage
from features.batch import BatchProcessor
from features.index import FeatureIndex, IndexStorage
from features.config import ConfigManager
from features.utils import setup_logger, create_performance_monitor
from features.search.similarity_search import SimilarityCalculator, SimilarityMetric

# 导入核心模块
from .data_models import SearchRequest, SearchResponse, ProcessingResult
from .search import SearchHandler
from .processing import FeatureProcessor
from .statistics import StatisticsCollector


class FeatureManager:
    """特征管理器
    
    重构后的特征管理器，提供以下功能：
    - 特征提取和存储
    - 批处理任务管理
    - 索引构建和搜索
    - 配置管理
    - 性能监控
    """
    
    def __init__(self, app_config, data_repository, gpu_manager, 
                 model_config_manager, config_dir: str = "./config"):
        """初始化特征管理器
        
        Args:
            app_config: 应用配置
            data_repository: 数据仓库
            gpu_manager: GPU管理器
            model_config_manager: 模型配置管理器
            config_dir: 配置文件目录
        """
        # 基础组件
        self.app_config = app_config
        self.data_repository = data_repository
        self.gpu_manager = gpu_manager
        self.model_config_manager = model_config_manager
        
        # 配置管理
        self.config_manager = ConfigManager(config_dir)
        self.config_manager.load_all_configs()
        
        # 日志设置
        self.logger = setup_logger(
            name=__name__,
            log_file=Path(config_dir) / "feature_manager.log",
            level=logging.INFO
        )
        
        # 特征存储
        self.feature_storage = FeatureStorage(
            fabric_repository=data_repository,
            cache_size=self.config_manager.performance_config.cache.feature_cache_size
        )
        
        # 特征索引
        self.feature_index = FeatureIndex()
        # 从配置中获取索引存储目录
        if hasattr(self.app_config, 'data_dir'):
            if isinstance(self.app_config.data_dir, Path):
                index_storage_dir = str(self.app_config.data_dir / 'index')
            else:
                index_storage_dir = str(Path(self.app_config.data_dir) / 'index')
        else:
            # 如果配置没有data_dir属性，使用默认路径
            project_root = Path(__file__).parent.parent.parent
            index_storage_dir = str(project_root / 'data' / 'index')
            self.logger.warning(f"配置缺少data_dir属性，使用默认路径: {index_storage_dir}")
        self.index_storage = IndexStorage(storage_dir=index_storage_dir)
        
        # 批处理器
        self.batch_processor: Optional[BatchProcessor] = None
        
        # 特征提取器和相似度计算器（延迟初始化）
        self.feature_extractor = None
        self.similarity_calculator = None
        
        # 性能监控
        self.performance_monitor = create_performance_monitor(
            "FeatureManager", self.logger
        )
        
        # 功能处理器
        self.search_handler: Optional[SearchHandler] = None
        self.feature_processor: Optional[FeatureProcessor] = None
        self.statistics_collector: Optional[StatisticsCollector] = None
        
        # 初始化所有组件
        try:
            if not self.initialize_components():
                self.logger.error("特征管理器组件初始化失败")
                # 不抛出异常，允许部分功能可用
        except Exception as e:
            self.logger.error(f"初始化特征管理器失败: {e}")
            # 不抛出异常，允许系统继续运行
            
        self.logger.info("特征管理器初始化完成")
        
    def initialize_components(self) -> bool:
        """初始化组件
        
        Returns:
            bool: 是否成功初始化
        """
        try:
            # 初始化特征提取器
            if not self._initialize_feature_extractor():
                return False
                
            # 初始化相似度计算器
            if not self._initialize_similarity_calculator():
                return False
                
            # 初始化批处理器
            self._initialize_batch_processor()
            
            # 初始化功能处理器
            self._initialize_handlers()
            
            self.logger.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            return False
            
    def _initialize_feature_extractor(self) -> bool:
        """初始化特征提取器"""
        try:
            from features.feature_extractor import FeatureExtractor
            from ..config.feature_config import FeatureExtractorConfig
            
            model_config = self.config_manager.model_config
            feature_config = self.config_manager.feature_config
            
            # 从FeatureConfig中提取深度特征配置
            deep_config = feature_config.deep_feature
            
            # 创建FeatureExtractorConfig实例
            extractor_config = FeatureExtractorConfig(
                model_name=deep_config.model_name,
                use_gpu=deep_config.use_gpu,
                batch_size=deep_config.batch_size,
                normalize_features=deep_config.normalize,
                use_cache=feature_config.cache_features
            )
            
            self.feature_extractor = FeatureExtractor(
                config=extractor_config,
                traditional_config=self.config_manager.feature_config.traditional_feature
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"特征提取器初始化失败: {e}")
            return False
            
    def _initialize_similarity_calculator(self) -> bool:
        """初始化相似度计算器"""
        try:
            search_config = self.config_manager.search_config
            
            # 获取相似度度量，如果没有则使用默认值
            metric = SimilarityMetric.COSINE
            if hasattr(search_config, 'similarity_metric'):
                # 将配置中的字符串转换为SimilarityMetric枚举
                metric_value = search_config.similarity_metric.value if hasattr(search_config.similarity_metric, 'value') else search_config.similarity_metric
                try:
                    metric = SimilarityMetric(metric_value)
                except ValueError:
                    self.logger.warning(f"不支持的相似度度量: {metric_value}，使用默认值 COSINE")
            
            self.similarity_calculator = SimilarityCalculator(
                metric=metric,
                use_gpu=True
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"相似度计算器初始化失败: {e}")
            return False
            
    def _initialize_batch_processor(self) -> None:
        """初始化批处理器"""
        performance_config = self.config_manager.performance_config
        
        self.batch_processor = BatchProcessor(
            feature_extractor=self.feature_extractor,
            feature_storage=self.feature_storage,
            max_workers=performance_config.threading.max_workers
        )
        
    def _initialize_handlers(self) -> None:
        """初始化功能处理器"""
        # 搜索处理器
        self.search_handler = SearchHandler(
            feature_extractor=self.feature_extractor,
            feature_storage=self.feature_storage,
            similarity_calculator=self.similarity_calculator,
            logger=self.logger
        )
        
        # 特征处理器
        self.feature_processor = FeatureProcessor(
            feature_extractor=self.feature_extractor,
            feature_storage=self.feature_storage,
            batch_processor=self.batch_processor,
            config_manager=self.config_manager,
            logger=self.logger
        )
        
        # 统计信息收集器
        self.statistics_collector = StatisticsCollector(
            feature_storage=self.feature_storage,
            similarity_calculator=self.similarity_calculator,
            batch_processor=self.batch_processor,
            performance_monitor=self.performance_monitor,
            logger=self.logger
        )
        
    # 特征处理相关方法
    def extract_and_store_features(self, image_paths: List[str], 
                                 batch_size: Optional[int] = None) -> ProcessingResult:
        """提取并存储特征"""
        if not self.feature_processor:
            raise RuntimeError("特征处理器未初始化")
        return self.feature_processor.extract_and_store_features(
            image_paths, self.performance_monitor, batch_size
        )
        
    def change_model(self, model_name: str) -> bool:
        """更改模型"""
        if not self.feature_processor:
            raise RuntimeError("特征处理器未初始化")
            
        # 更新配置
        if not self.feature_processor.change_model(model_name):
            return False
            
        # 重新初始化特征提取器
        if not self._initialize_feature_extractor():
            return False
            
        # 重新初始化处理器
        self._initialize_handlers()
        
        # 清空缓存和索引
        self.clear_cache()
        
        return True
        
    def clear_cache(self) -> None:
        """清空缓存"""
        try:
            if self.feature_processor:
                self.feature_processor.clear_cache()
                
            if self.similarity_calculator:
                self.similarity_calculator.clear_index()
                
            self.logger.info("缓存已清空")
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            
    # 搜索相关方法
    def search_similar_images(self, request: SearchRequest) -> SearchResponse:
        """搜索相似图像"""
        if not self.search_handler:
            raise RuntimeError("搜索处理器未初始化")
        return self.search_handler.search_similar_images(request, self.performance_monitor)
        
    def build_search_index(self, force_rebuild: bool = False) -> bool:
        """构建搜索索引"""
        if not self.search_handler:
            raise RuntimeError("搜索处理器未初始化")
        return self.search_handler.build_search_index(self.performance_monitor, force_rebuild)
        
    def save_index(self, index_path: str) -> bool:
        """保存索引"""
        if not self.search_handler:
            raise RuntimeError("搜索处理器未初始化")
        return self.search_handler.save_index(index_path)
        
    def load_index(self, index_path: str) -> bool:
        """加载索引"""
        if not self.search_handler:
            raise RuntimeError("搜索处理器未初始化")
        return self.search_handler.load_index(index_path)
        
    # 统计信息相关方法
    def get_feature_statistics(self) -> Dict[str, Any]:
        """获取特征统计信息"""
        if not self.statistics_collector:
            raise RuntimeError("统计信息收集器未初始化")
        return self.statistics_collector.get_feature_statistics()
        
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """获取综合报告"""
        if not self.statistics_collector:
            raise RuntimeError("统计信息收集器未初始化")
        return self.statistics_collector.get_comprehensive_report()
        
    # 资源管理
    def cleanup(self) -> None:
        """清理资源"""
        # 添加静态类变量来跟踪清理状态
        if not hasattr(FeatureManager, '_cleanup_called'):
            FeatureManager._cleanup_called = False
            
        # 如果已经清理过，则跳过
        if FeatureManager._cleanup_called:
            self.logger.debug("FeatureManager资源已清理，跳过重复清理")
            return
            
        try:
            self.logger.info("开始清理FeatureManager资源")
            
            if hasattr(self, 'feature_processor') and self.feature_processor:
                self.logger.debug("清理feature_processor")
                self.feature_processor.cleanup()
                
            if hasattr(self, 'similarity_calculator') and self.similarity_calculator:
                self.logger.debug("清理similarity_calculator")
                self.similarity_calculator.cleanup()
                
            if hasattr(self, 'feature_storage') and self.feature_storage:
                self.logger.debug("清理feature_storage")
                self.feature_storage.clear_cache()
                
            if hasattr(self, 'batch_processor') and self.batch_processor:
                self.logger.debug("清理batch_processor")
                self.batch_processor.shutdown()
                
            if hasattr(self, 'search_handler') and self.search_handler:
                self.logger.debug("清理search_handler")
                self.search_handler.cleanup()
                
            if hasattr(self, 'statistics_collector') and self.statistics_collector:
                self.logger.debug("清理statistics_collector")
                self.statistics_collector.cleanup()
                
            # 标记为已清理
            FeatureManager._cleanup_called = True
            self.logger.info("FeatureManager资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
            
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception:
            # 忽略析构函数中的异常，避免程序崩溃
            pass