#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控件工厂主类

该模块提供统一的控件工厂接口。
"""

from typing import Any
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve
from PyQt6.QtWidgets import QWidget
from PyQt6.QtGui import QAction, QKeySequence, QIcon, QFont
from PyQt6.QtCore import QSize

from utils.log_utils import LoggerMixin
from ..helpers import IconHelper
from .base import WidgetConfig
from .button_factory import ButtonFactory
from .input_factory import InputFactory
from .layout_factory import LayoutFactory
from .container_factory import ContainerFactory


class WidgetFactory(LoggerMixin):
    """统一的控件工厂"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化子工厂
        self.button_factory = ButtonFactory()
        self.input_factory = InputFactory()
        self.layout_factory = LayoutFactory()
        self.container_factory = ContainerFactory()
        
        # 默认配置
        self.default_icon_size = QSize(16, 16)
    
    # 按钮相关方法
    def create_button(self, *args, **kwargs):
        """创建按钮"""
        return self.button_factory.create_button(*args, **kwargs)
    
    def create_tool_button(self, *args, **kwargs):
        """创建工具按钮"""
        return self.button_factory.create_tool_button(*args, **kwargs)
    
    # 输入控件相关方法
    def create_label(self, *args, **kwargs):
        """创建标签"""
        return self.input_factory.create_label(*args, **kwargs)
    
    def create_line_edit(self, *args, **kwargs):
        """创建单行输入框"""
        return self.input_factory.create_line_edit(*args, **kwargs)
    
    def create_text_edit(self, *args, **kwargs):
        """创建多行文本编辑器"""
        return self.input_factory.create_text_edit(*args, **kwargs)
    
    def create_combo_box(self, *args, **kwargs):
        """创建组合框"""
        return self.input_factory.create_combo_box(*args, **kwargs)
    
    def create_spin_box(self, *args, **kwargs):
        """创建整数调节框"""
        return self.input_factory.create_spin_box(*args, **kwargs)
    
    def create_double_spin_box(self, *args, **kwargs):
        """创建浮点数调节框"""
        return self.input_factory.create_double_spin_box(*args, **kwargs)
    
    def create_slider(self, *args, **kwargs):
        """创建滑块"""
        return self.input_factory.create_slider(*args, **kwargs)
    
    def create_checkbox(self, *args, **kwargs):
        """创建复选框"""
        return self.input_factory.create_checkbox(*args, **kwargs)
    
    def create_radio_button(self, *args, **kwargs):
        """创建单选按钮"""
        return self.input_factory.create_radio_button(*args, **kwargs)
    
    def create_button_group(self, *args, **kwargs):
        """创建按钮组"""
        return self.input_factory.create_button_group(*args, **kwargs)
    
    # 布局相关方法
    def create_vbox_layout(self, *args, **kwargs):
        """创建垂直布局"""
        return self.layout_factory.create_vbox_layout(*args, **kwargs)
    
    def create_hbox_layout(self, *args, **kwargs):
        """创建水平布局"""
        return self.layout_factory.create_hbox_layout(*args, **kwargs)
    
    def create_grid_layout(self, *args, **kwargs):
        """创建网格布局"""
        return self.layout_factory.create_grid_layout(*args, **kwargs)
    
    def create_form_layout(self, *args, **kwargs):
        """创建表单布局"""
        return self.layout_factory.create_form_layout(*args, **kwargs)
    
    def create_spacer(self, *args, **kwargs):
        """创建空白间隔"""
        return self.layout_factory.create_spacer(*args, **kwargs)
    
    # 容器相关方法
    def create_group_box(self, *args, **kwargs):
        """创建分组框"""
        return self.container_factory.create_group_box(*args, **kwargs)
    
    def create_frame(self, *args, **kwargs):
        """创建框架"""
        return self.container_factory.create_frame(*args, **kwargs)
    
    def create_tab_widget(self, *args, **kwargs):
        """创建标签页控件"""
        return self.container_factory.create_tab_widget(*args, **kwargs)
    
    def create_splitter(self, *args, **kwargs):
        """创建分割器"""
        return self.container_factory.create_splitter(*args, **kwargs)
    
    def create_scroll_area(self, *args, **kwargs):
        """创建滚动区域"""
        return self.container_factory.create_scroll_area(*args, **kwargs)
    
    def create_stacked_widget(self, *args, **kwargs):
        """创建堆叠控件"""
        return self.container_factory.create_stacked_widget(*args, **kwargs)
    
    def create_progress_bar(self, *args, **kwargs):
        """创建进度条"""
        return self.container_factory.create_progress_bar(*args, **kwargs)
    
    def create_list_widget(self, *args, **kwargs):
        """创建列表控件"""
        return self.container_factory.create_list_widget(*args, **kwargs)
    
    def create_tree_widget(self, *args, **kwargs):
        """创建树形控件"""
        return self.container_factory.create_tree_widget(*args, **kwargs)
    
    def create_table_widget(self, *args, **kwargs):
        """创建表格控件"""
        return self.container_factory.create_table_widget(*args, **kwargs)
    
    def create_separator(self, *args, **kwargs):
        """创建分隔线"""
        return self.container_factory.create_separator(*args, **kwargs)
    
    # 其他工具方法
    def create_action(self, text: str, icon=None, shortcut=None,
                     tooltip: str = "", checkable: bool = False,
                     triggered_handler=None) -> QAction:
        """创建动作
        
        Args:
            text: 动作文本
            icon: 图标
            shortcut: 快捷键
            tooltip: 工具提示
            checkable: 是否可选中
            triggered_handler: 触发事件处理器
            
        Returns:
            QAction: 动作对象
        """
        try:
            action = QAction(text)
            
            # 设置图标
            if icon:
                if isinstance(icon, str):
                    action.setIcon(IconHelper.load_icon(icon, self.default_icon_size))
                else:
                    action.setIcon(icon)
            
            # 设置快捷键
            if shortcut:
                if isinstance(shortcut, str):
                    action.setShortcut(QKeySequence(shortcut))
                else:
                    action.setShortcut(shortcut)
            
            # 设置工具提示
            if tooltip:
                action.setToolTip(tooltip)
            
            # 设置可选中
            action.setCheckable(checkable)
            
            # 连接事件
            if triggered_handler:
                action.triggered.connect(triggered_handler)
            
            return action
            
        except Exception as e:
            self.logger.error(f"创建动作失败: {e}")
            return QAction(text)
    
    def apply_animation(self, widget: QWidget, property_name: str,
                       start_value: Any, end_value: Any, duration: int = 300,
                       easing_curve: QEasingCurve.Type = QEasingCurve.Type.OutCubic) -> QPropertyAnimation:
        """应用动画
        
        Args:
            widget: 目标控件
            property_name: 属性名称
            start_value: 起始值
            end_value: 结束值
            duration: 动画时长
            easing_curve: 缓动曲线
            
        Returns:
            QPropertyAnimation: 动画对象
        """
        try:
            animation = QPropertyAnimation(widget, property_name.encode())
            animation.setDuration(duration)
            animation.setStartValue(start_value)
            animation.setEndValue(end_value)
            animation.setEasingCurve(easing_curve)
            
            return animation
            
        except Exception as e:
            self.logger.error(f"应用动画失败: {e}")
            return QPropertyAnimation(widget, property_name.encode())
    
    def set_default_font(self, font: QFont):
        """设置默认字体
        
        Args:
            font: 字体对象
        """
        # 传递给子工厂
        pass
    
    def set_default_icon_size(self, size: QSize):
        """设置默认图标尺寸
        
        Args:
            size: 图标尺寸
        """
        self.default_icon_size = size
        self.button_factory.set_default_icon_size(size)
    
    def cleanup(self):
        """清理资源"""
        try:
            self.logger.debug("控件工厂资源清理完成")
        except Exception as e:
            self.logger.error(f"控件工厂资源清理失败: {e}")