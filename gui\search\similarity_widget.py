#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相似度搜索组件

该模块提供基于图像相似度的搜索功能界面。
"""

import os
from typing import Optional, Dict, Any

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QSpinBox,
    QCheckBox, QGroupBox, QFrame, QScrollArea, QFormLayout
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QDragEnterEvent, QDropEvent

from utils.log_utils import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.helpers.file_helper import FileDialogHelper
from gui.widget_factory import WidgetFactory, ButtonStyle, ButtonSize
from .feature_weights_widget import FeatureWeightsWidget
from .feature_params_widget import FeatureParamsWidget


class SimilaritySearchWidget(QWidget, LoggerMixin):
    """相似度搜索组件"""
    
    # 信号
    searchRequested = pyqtSignal(str, dict)  # 搜索请求
    imageLoaded = pyqtSignal(str)  # 图像加载
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self.current_image_path = None
        
        # 创建子组件
        self.feature_weights_widget = FeatureWeightsWidget()
        self.feature_params_widget = FeatureParamsWidget()
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建滚动区域内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(10)
        
        # 图像选择区域
        self._setup_image_selection(scroll_layout)
        
        # 搜索参数
        self._setup_search_params(scroll_layout)
        
        # 高级参数 - 特征权重设置
        scroll_layout.addWidget(self.feature_weights_widget)
        
        # 传统特征参数设置
        scroll_layout.addWidget(self.feature_params_widget)
        
        # 搜索按钮
        self.search_btn = self.widget_factory.create_button(
            "开始搜索", "search", None, ButtonStyle.SUCCESS, ButtonSize.LARGE,
            click_handler=self.start_search
        )
        self.search_btn.setEnabled(False)
        scroll_layout.addWidget(self.search_btn)
        
        scroll_layout.addStretch()
        
        # 设置滚动区域内容并添加到主布局
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
    
    def _setup_image_selection(self, layout):
        """设置图像选择区域"""
        image_group = self.widget_factory.create_group_box("选择查询图像")
        image_layout = QVBoxLayout(image_group)
        
        # 图像预览
        self.image_preview = QLabel()
        self.image_preview.setFixedSize(200, 200)
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setStyleSheet(
            "border: 2px dashed #ccc; background-color: #f9f9f9;"
        )
        self.image_preview.setText("点击选择图像\n或拖拽到此处")
        self.image_preview.setAcceptDrops(True)
        self.image_preview.mousePressEvent = self.select_image
        self.image_preview.dragEnterEvent = self.drag_enter_event
        self.image_preview.dropEvent = self.drop_event
        image_layout.addWidget(self.image_preview, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.select_btn = self.widget_factory.create_button(
            "选择图像", "folder", None, ButtonStyle.PRIMARY, ButtonSize.MEDIUM,
            click_handler=self.select_image
        )
        button_layout.addWidget(self.select_btn)
        
        self.clear_btn = self.widget_factory.create_button(
            "清除", "clear", None, ButtonStyle.SECONDARY, ButtonSize.MEDIUM,
            click_handler=self.clear_image
        )
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        image_layout.addLayout(button_layout)
        
        layout.addWidget(image_group)
    
    def _setup_search_params(self, layout):
        """设置搜索参数"""
        params_group = self.widget_factory.create_group_box("搜索参数")
        params_layout = QFormLayout(params_group)
        
        # 相似度阈值
        self.similarity_slider = self.widget_factory.create_slider(
            minimum=1, maximum=100, value=30,
            orientation=Qt.Orientation.Horizontal,
            tick_position=QSlider.TickPosition.TicksBelow
        )
        self.similarity_label = self.widget_factory.create_label("相似度要求: 30%")
        self.similarity_label.setMinimumWidth(120)
        
        similarity_layout = QHBoxLayout()
        similarity_layout.addWidget(self.similarity_slider)
        similarity_layout.addWidget(self.similarity_label)
        
        params_layout.addRow("相似度要求:", similarity_layout)
        
        # 最大结果数
        try:
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_max_results = config_manager.config.search.top_k
        except Exception:
            default_max_results = 50
            
        self.max_results_spin = self.widget_factory.create_spin_box(
            minimum=1, maximum=1000, value=default_max_results
        )
        params_layout.addRow("最大结果数:", self.max_results_spin)
        
        layout.addWidget(params_group)
    

    

    

    
    def connect_signals(self):
        """连接信号"""
        self.similarity_slider.valueChanged.connect(self.update_similarity_label)
    
    def select_image(self, event=None):
        """选择图像"""
        try:
            file_path, _ = FileDialogHelper.get_open_file_name(
                self, "选择图像文件", "",
                "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff *.gif);;所有文件 (*)"
            )
            
            if file_path:
                self.load_image(file_path)
                
        except Exception as e:
            self.logger.error(f"选择图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"选择图像失败: {e}")
    
    def load_image(self, image_path: str):
        """加载图像
        
        Args:
            image_path: 图像路径
        """
        try:
            # 使用统一的图像验证逻辑
            from utils.image_validation import get_image_info
            
            # 获取图像信息并验证
            image_info = get_image_info(image_path)
            
            if not image_info['exists']:
                MessageHelper.show_warning(self, "警告", f"文件不存在: {image_path}")
                return
            
            if not image_info['valid_path']:
                MessageHelper.show_warning(self, "警告", 
                    f"不支持的图像格式: {image_path}\n"
                    f"错误: {image_info.get('error', '未知错误')}")
                return
            
            if not image_info['valid_content']:
                MessageHelper.show_warning(self, "警告", 
                    f"图像文件损坏或格式不正确: {image_path}\n"
                    f"错误: {image_info.get('error', '未知错误')}")
                return
            
            # 加载图像
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                MessageHelper.show_warning(self, "警告", f"无法加载图像: {image_path}")
                return
            
            # 缩放图像以适应预览区域
            scaled_pixmap = pixmap.scaled(
                self.image_preview.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            
            # 更新预览
            self.image_preview.setPixmap(scaled_pixmap)
            self.current_image_path = image_path
            
            # 启用搜索按钮
            self.search_btn.setEnabled(True)
            
            # 发送图像加载信号
            self.imageLoaded.emit(image_path)
            
            self.logger.info(f"图像加载成功: {image_path}")
            
        except Exception as e:
            self.logger.error(f"加载图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"加载图像失败: {e}")
    
    def clear_image(self):
        """清除图像"""
        try:
            self.image_preview.clear()
            self.image_preview.setText("点击选择图像\n或拖拽到此处")
            self.current_image_path = None
            self.search_btn.setEnabled(False)
            
        except Exception as e:
            self.logger.error(f"清除图像失败: {e}")
    
    def drag_enter_event(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def drop_event(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            self.load_image(file_path)
    
    def update_similarity_label(self, value: int):
        """更新相似度标签"""
        self.similarity_label.setText(f"相似度要求: {value}%")
        
        # 设置工具提示
        tooltip = f"相似度阈值: {value/100:.2f}\n\n"
        tooltip += "滑块值越大，表示要求的相似度越高:\n"
        tooltip += "- 较低的阈值（如10%）会返回更多结果，包括相似度较低的图像\n"
        tooltip += "- 较高的阈值（如80%）会返回更少但更相似的结果\n"
        tooltip += "\n建议值: \n"
        tooltip += "- 低(10-30%): 返回更多可能相关的结果\n"
        tooltip += "- 中(40-60%): 平衡结果数量和相似度\n"
        tooltip += "- 高(70-90%): 仅返回高度相似的结果\n\n"
        tooltip += "注意: 如果设置过高可能导致没有结果返回"
        
        self.similarity_label.setToolTip(tooltip)
        self.similarity_slider.setToolTip(tooltip)
        
        # 记录日志
        self.logger.debug(f"相似度要求更新为: {value}% (阈值={value/100:.2f})")
        
        # 根据阈值值设置不同的样式
        if value < 30:
            self.similarity_label.setStyleSheet("color: green;")
        elif value < 70:
            self.similarity_label.setStyleSheet("color: blue;")
        else:
            self.similarity_label.setStyleSheet("color: red;")
    

    
    def start_search(self):
        """开始搜索"""
        try:
            if not self.current_image_path:
                MessageHelper.show_warning(self, "警告", "请先选择查询图像")
                self.search_btn.setEnabled(False)
                return
            
            if not os.path.exists(self.current_image_path):
                MessageHelper.show_warning(self, "警告", f"文件不存在: {self.current_image_path}")
                self.clear_image()
                return
            
            # 构建搜索参数
            params = self.get_search_params()
            
            # 发送搜索请求
            self.searchRequested.emit(self.current_image_path, params)
            
        except Exception as e:
            self.logger.error(f"开始搜索失败: {e}")
            MessageHelper.show_error(self, "错误", f"开始搜索失败: {e}")
            self.search_btn.setEnabled(False)
    
    def get_current_image_path(self) -> Optional[str]:
        """获取当前图像路径"""
        return self.current_image_path
    
    def get_search_params(self) -> Dict[str, Any]:
        """获取搜索参数"""
        # 从特征权重组件获取权重
        weights = self.feature_weights_widget.get_weights()
        
        # 从传统特征参数组件获取参数
        feature_extraction_params = self.feature_params_widget.get_params()
        
        # 将UI中的百分比值(1-100)转换为浮点数(0.0-1.0)作为相似度阈值
        ui_value = self.similarity_slider.value()
        similarity_threshold = ui_value / 100.0
        
        # 确保阈值在有效范围内
        similarity_threshold = max(0.0, min(1.0, similarity_threshold))
        
        # 记录日志
        self.logger.info(f"设置相似度要求: UI值={ui_value}%, 阈值={similarity_threshold:.2f}")
        
        # 如果阈值较高，添加警告日志
        if similarity_threshold > 0.7:
            self.logger.warning(f"相似度要求设置较高({ui_value}%，阈值={similarity_threshold:.2f})，可能导致结果较少或无结果")
        
        return {
            "similarity_threshold": similarity_threshold,
            "max_results": self.max_results_spin.value(),
            "feature_weights": weights,
            "feature_extraction_params": feature_extraction_params,
            "strategy_name": "weighted"  # 使用加权搜索策略
        }
        
    def set_searching(self, is_searching: bool):
        """设置搜索状态
        
        Args:
            is_searching: 是否正在搜索
        """
        try:
            # 更新搜索按钮状态
            if hasattr(self, 'search_btn'):
                self.search_btn.setEnabled(not is_searching)
                self.search_btn.setText("停止搜索" if is_searching else "开始搜索")
            
            # 更新其他UI元素状态
            if hasattr(self, 'load_image_btn'):
                self.load_image_btn.setEnabled(not is_searching)
            
            # 记录日志
            self.logger.debug(f"相似度搜索组件状态已更新: {'搜索中' if is_searching else '空闲'}")
            
        except Exception as e:
            self.logger.error(f"设置搜索状态失败: {e}")