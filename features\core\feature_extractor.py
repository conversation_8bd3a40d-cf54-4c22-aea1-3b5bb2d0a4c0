#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心特征提取器

重构后的特征提取器核心模块，专注于特征提取逻辑。
"""

import logging
import time
from typing import Optional, Dict
import numpy as np
from PIL import Image

from ..config.feature_config import FeatureExtractorConfig, TraditionalFeatureConfig
from ..data_models.feature_models import FeatureExtractionResult
from ..extractors.deep_extractor import DeepFeatureExtractor
from ..extractors.traditional_extractor import TraditionalFeatureExtractor
from ..validators.input_validator import InputValidator
from ..validators.image_validator import ImageValidator
from ..storage.cache_manager import CacheManager
from ..utils.image_utils import load_image
from ..utils.feature_utils import validate_features

logger = logging.getLogger(__name__)


class FeatureExtractor:
    """重构后的统一特征提取器"""
    
    def __init__(self, config: FeatureExtractorConfig, 
                 traditional_config: Optional[TraditionalFeatureConfig] = None):
        """初始化特征提取器
        
        Args:
            config: 深度特征提取器配置
            traditional_config: 传统特征提取器配置
        """
        # 验证配置
        if not InputValidator.validate_config(config):
            raise ValueError("无效的配置对象")
        
        self.config = config
        self.traditional_config = traditional_config or TraditionalFeatureConfig()
        
        # 初始化提取器
        self.deep_extractor = DeepFeatureExtractor(config)
        self.traditional_extractor = TraditionalFeatureExtractor(self.traditional_config)
        
        # 初始化缓存管理器
        self.cache_manager = CacheManager(
            cache_dir=config.cache_dir,
            use_cache=config.use_cache
        )
        
        logger.info(f"Initialized FeatureExtractor with model {config.model_name}")
    
    def extract_features(self, image_path: str, 
                        extract_traditional: bool = True) -> FeatureExtractionResult:
        """提取图像特征
        
        Args:
            image_path: 图像路径
            extract_traditional: 是否提取传统特征
            
        Returns:
            FeatureExtractionResult: 特征提取结果
        """
        start_time = time.time()
        
        try:
            # 输入验证
            validation_result = InputValidator.validate_image_path(image_path)
            if not validation_result.success:
                return validation_result
            
            image_path = validation_result.image_path
            
            # 图像内容验证
            content_validation = ImageValidator.validate_image_content(image_path)
            if not content_validation.success:
                return content_validation
            
            # 检查缓存
            cached_result = self.cache_manager.load_from_cache(image_path)
            if cached_result is not None:
                # 验证缓存的特征
                if (cached_result.features is not None and 
                    len(cached_result.features) > 0 and
                    not np.isnan(cached_result.features).any() and
                    not np.isinf(cached_result.features).any()):
                    logger.debug(f"从缓存加载特征: {image_path}")
                    return cached_result
                else:
                    logger.warning(f"缓存特征无效，重新提取: {image_path}")
            
            # 加载图像
            image = self._load_and_validate_image(image_path)
            if image is None:
                return FeatureExtractionResult(
                    image_path=image_path,
                    success=False,
                    error_message=f"无法加载图像: {image_path}"
                )
            
            # 提取深度特征
            deep_features = self._extract_deep_features(image, image_path)
            if deep_features is None:
                return FeatureExtractionResult(
                    image_path=image_path,
                    success=False,
                    error_message="深度特征提取失败"
                )
            
            # 创建结果对象
            result = FeatureExtractionResult(
                image_path=image_path,
                success=True,
                features=deep_features,
                extraction_time=time.time() - start_time,
                model_name=self.config.model_name,
                feature_dimension=len(deep_features)
            )
            
            # 添加深度特征向量
            result.add_feature_vector(
                "deep", 
                deep_features, 
                self.config.model_name, 
                time.time() - start_time
            )
            
            # 提取传统特征
            if extract_traditional:
                self._extract_and_add_traditional_features(image, result, image_path)
            
            # 更新总提取时间
            result.extraction_time = time.time() - start_time
            
            # 保存到缓存
            self.cache_manager.save_to_cache(image_path, result)
            
            logger.debug(f"特征提取成功: {image_path}, 耗时: {result.extraction_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"特征提取异常: {image_path} - {str(e)}", exc_info=True)
            return FeatureExtractionResult(
                image_path=image_path if 'image_path' in locals() else "",
                success=False,
                error_message=f"特征提取异常: {str(e)}",
                extraction_time=time.time() - start_time,
                model_name=self.config.model_name
            )
    
    def extract_features_from_image(self, image: Image.Image, 
                                   extract_traditional: bool = True) -> Dict[str, np.ndarray]:
        """从图像对象提取特征
        
        Args:
            image: 图像对象
            extract_traditional: 是否提取传统特征
            
        Returns:
            Dict[str, np.ndarray]: 特征字典
        """
        try:
            # 验证图像对象
            if not ImageValidator.validate_image_object(image):
                return {'deep': np.zeros(self._get_default_feature_dimension(), dtype=np.float32)}
            
            features = {}
            
            # 提取深度特征
            deep_features = self._extract_deep_features(image)
            if deep_features is not None:
                features['deep'] = deep_features
            else:
                features['deep'] = np.zeros(self._get_default_feature_dimension(), dtype=np.float32)
            
            # 提取传统特征
            if extract_traditional:
                traditional_features = self._extract_traditional_features(image)
                if traditional_features:
                    features.update(traditional_features)
            
            return features
            
        except Exception as e:
            logger.error(f"从图像对象提取特征异常: {str(e)}", exc_info=True)
            return {'deep': np.zeros(self._get_default_feature_dimension(), dtype=np.float32)}
    
    def change_model(self, model_name: str):
        """更换深度学习模型
        
        Args:
            model_name: 新模型名称
        """
        try:
            if not model_name or not isinstance(model_name, str):
                raise ValueError("模型名称必须是非空字符串")
            
            old_model = self.config.model_name
            
            # 更新配置
            self.config.model_name = model_name
            
            # 重新初始化深度提取器
            self.deep_extractor = DeepFeatureExtractor(self.config)
            
            logger.info(f"模型更换成功: {old_model} -> {model_name}")
            
        except Exception as e:
            logger.error(f"模型更换失败: {str(e)}")
            raise
    
    def _load_and_validate_image(self, image_path: str) -> Optional[Image.Image]:
        """加载并验证图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            Optional[Image.Image]: 图像对象或None
        """
        try:
            image = load_image(image_path)
            if image is None:
                return None
            
            if not ImageValidator.validate_image_object(image):
                return None
            
            return image
            
        except Exception as e:
            logger.error(f"图像加载异常: {image_path} - {str(e)}")
            return None
    
    def _extract_deep_features(self, image: Image.Image, 
                              image_path: str = "") -> Optional[np.ndarray]:
        """提取深度特征
        
        Args:
            image: 图像对象
            image_path: 图像路径（用于日志）
            
        Returns:
            Optional[np.ndarray]: 深度特征或None
        """
        try:
            if not hasattr(self, 'deep_extractor') or self.deep_extractor is None:
                logger.error("深度特征提取器未初始化")
                return None
            
            deep_features = self.deep_extractor.extract_features_from_image(image)
            
            # 验证深度特征
            if deep_features is None:
                logger.error(f"深度特征提取返回None: {image_path}")
                return None
            
            if not isinstance(deep_features, np.ndarray):
                try:
                    deep_features = np.array(deep_features, dtype=np.float32)
                except Exception as e:
                    logger.error(f"深度特征类型转换失败: {image_path} - {str(e)}")
                    return None
            
            if deep_features.size == 0:
                logger.error(f"深度特征为空数组: {image_path}")
                return None
            
            # 确保特征是一维数组
            if deep_features.ndim != 1:
                deep_features = deep_features.flatten()
            
            # 检查特征是否包含异常值
            if np.isnan(deep_features).any() or np.isinf(deep_features).any():
                logger.warning(f"深度特征包含NaN或无穷大值: {image_path}")
                deep_features = np.nan_to_num(deep_features, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 确保特征数据类型
            deep_features = deep_features.astype(np.float32)
            
            return deep_features
            
        except Exception as e:
            logger.error(f"深度特征提取异常: {image_path} - {str(e)}")
            return None
    
    def _extract_traditional_features(self, image: Image.Image) -> Dict[str, np.ndarray]:
        """提取传统特征
        
        Args:
            image: 图像对象
            
        Returns:
            Dict[str, np.ndarray]: 传统特征字典
        """
        features = {}
        
        try:
            if not hasattr(self, 'traditional_extractor') or self.traditional_extractor is None:
                return features
            
            traditional_features = self.traditional_extractor.extract_features(image)
            
            if isinstance(traditional_features, dict):
                for feature_type, feature_values in traditional_features.items():
                    processed_features = self._process_traditional_feature(
                        feature_type, feature_values
                    )
                    if processed_features is not None:
                        features[feature_type] = processed_features
            
        except Exception as e:
            logger.warning(f"传统特征提取失败: {str(e)}")
        
        return features
    
    def _extract_and_add_traditional_features(self, image: Image.Image, 
                                            result: FeatureExtractionResult,
                                            image_path: str = ""):
        """提取并添加传统特征到结果中
        
        Args:
            image: 图像对象
            result: 特征提取结果对象
            image_path: 图像路径（用于日志）
        """
        try:
            traditional_features = self._extract_traditional_features(image)
            
            for feature_type, features in traditional_features.items():
                try:
                    result.add_feature_vector(feature_type, features)
                except Exception as e:
                    logger.warning(f"添加传统特征 {feature_type} 失败: {image_path} - {str(e)}")
                    
        except Exception as e:
            logger.warning(f"传统特征提取失败: {image_path} - {str(e)}")
    
    def _process_traditional_feature(self, feature_type: str, 
                                   feature_values) -> Optional[np.ndarray]:
        """处理传统特征
        
        Args:
            feature_type: 特征类型
            feature_values: 特征值
            
        Returns:
            Optional[np.ndarray]: 处理后的特征或None
        """
        try:
            if feature_values is None:
                logger.warning(f"传统特征 {feature_type} 为None")
                return None
            
            if not isinstance(feature_values, np.ndarray):
                try:
                    feature_values = np.array(feature_values, dtype=np.float32)
                except Exception as e:
                    logger.warning(f"传统特征 {feature_type} 类型转换失败: {str(e)}")
                    return None
            
            if feature_values.size == 0:
                logger.warning(f"传统特征 {feature_type} 为空")
                return None
            
            # 确保特征是一维数组
            if feature_values.ndim != 1:
                feature_values = feature_values.flatten()
            
            # 检查传统特征的异常值
            if np.isnan(feature_values).any() or np.isinf(feature_values).any():
                logger.warning(f"传统特征 {feature_type} 包含异常值")
                feature_values = np.nan_to_num(feature_values, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 确保特征数据类型
            feature_values = feature_values.astype(np.float32)
            
            if validate_features(feature_values):
                return feature_values
            else:
                logger.warning(f"传统特征 {feature_type} 验证失败")
                return None
                
        except Exception as e:
            logger.warning(f"处理传统特征 {feature_type} 失败: {str(e)}")
            return None
    
    def _get_default_feature_dimension(self) -> int:
        """获取默认特征维度
        
        Returns:
            int: 默认特征维度
        """
        try:
            if hasattr(self, 'deep_extractor') and self.deep_extractor:
                return self.deep_extractor.get_feature_dimension()
            return 512
        except Exception:
            return 512
    
    def cleanup(self):
        """清理资源"""
        # 添加静态类变量来跟踪清理状态
        if not hasattr(FeatureExtractor, '_cleanup_called'):
            FeatureExtractor._cleanup_called = False
            
        # 如果已经清理过，则跳过
        if FeatureExtractor._cleanup_called:
            logger.debug("FeatureExtractor资源已清理，跳过重复清理")
            return
            
        try:
            logger.info("开始清理FeatureExtractor资源")
            
            if hasattr(self, 'deep_extractor') and self.deep_extractor:
                self.deep_extractor.cleanup()
                
            # 标记为已清理
            FeatureExtractor._cleanup_called = True
            logger.info("FeatureExtractor资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时出错: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()