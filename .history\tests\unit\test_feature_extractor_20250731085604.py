#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取器单元测试

测试特征提取器类的各种功能。
"""

import numpy as np
from PIL import Image
from unittest.mock import Mock

from features.core.feature_extractor import FeatureExtractor
from features.extractors.color_extractor import ColorFeatureExtractor
from features.extractors.texture_extractor import TextureFeatureExtractor
from features.extractors.shape_extractor import ShapeFeatureExtractor
from features.config.feature_config import TraditionalFeatureConfig


class TestFeatureExtractor:
    """特征提取器测试类"""

    def test_init(self):
        """测试特征提取器初始化"""
        extractor = FeatureExtractor()
        assert extractor is not None
        assert hasattr(extractor, 'extractors')

    def test_register_extractor(self):
        """测试提取器注册"""
        extractor = FeatureExtractor()
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        
        extractor.register_extractor(mock_extractor)
        assert 'test_extractor' in extractor.extractors

    def test_extract_features(self):
        """测试特征提取"""
        extractor = FeatureExtractor()
        
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # 模拟提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.extract_features.return_value = np.array([1, 2, 3])
        
        extractor.register_extractor(mock_extractor)
        
        features = extractor.extract_features(test_image)
        assert isinstance(features, dict)
        assert 'test_extractor' in features

    def test_batch_extract_features(self):
        """测试批量特征提取"""
        extractor = FeatureExtractor()
        
        # 创建测试图像列表
        test_images = [
            Image.new('RGB', (100, 100), color='red'),
            Image.new('RGB', (100, 100), color='blue')
        ]
        
        # 模拟提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.extract_features.return_value = np.array([1, 2, 3])
        
        extractor.register_extractor(mock_extractor)
        
        features_list = extractor.batch_extract_features(test_images)
        assert len(features_list) == 2
        assert all(isinstance(f, dict) for f in features_list)

    def test_extract_invalid_image(self):
        """测试无效图像处理"""
        extractor = FeatureExtractor()
        
        # 测试None图像
        features = extractor.extract_features(None)
        assert features == {}

        # 测试非图像对象
        features = extractor.extract_features("not_an_image")
        assert features == {}

    def test_get_extractor_info(self):
        """测试获取提取器信息"""
        extractor = FeatureExtractor()
        
        # 模拟提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.get_info.return_value = {'type': 'test'}
        
        extractor.register_extractor(mock_extractor)
        
        info = extractor.get_extractor_info('test_extractor')
        assert info == {'type': 'test'}


class TestColorExtractor:
    """颜色特征提取器测试类"""

    def setup_method(self):
        """设置测试方法"""
        self.config = TraditionalFeatureConfig()
        self.extractor = ColorFeatureExtractor(self.config)

    def test_init(self):
        """测试颜色提取器初始化"""
        assert self.extractor is not None
        assert self.extractor.config == self.config

    def test_extract_color_histogram(self):
        """测试颜色直方图提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        histogram = self.extractor.extract_color_histogram(test_image)
        assert isinstance(histogram, np.ndarray)
        assert len(histogram) > 0

    def test_extract_dominant_colors(self):
        """测试主要颜色提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        colors = self.extractor.extract_dominant_colors(test_image)
        assert isinstance(colors, np.ndarray)
        assert len(colors) > 0

    def test_extract_color_moments(self):
        """测试颜色矩提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        moments = self.extractor.extract_color_moments(test_image)
        assert isinstance(moments, np.ndarray)
        assert len(moments) > 0

    def test_extract_features(self):
        """测试完整特征提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        features = self.extractor.extract_features(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0

    def test_extract_invalid_image(self):
        """测试无效图像处理"""
        # 测试None图像
        features = self.extractor.extract_features(None)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0

        # 测试空图像
        empty_image = Image.new('RGB', (0, 0))
        features = self.extractor.extract_features(empty_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0


class TestTextureExtractor:
    """纹理特征提取器测试类"""

    def setup_method(self):
        """设置测试方法"""
        self.config = TraditionalFeatureConfig()
        self.extractor = TextureFeatureExtractor(self.config)

    def test_init(self):
        """测试纹理提取器初始化"""
        assert self.extractor is not None
        assert self.extractor.config == self.config

    def test_extract_glcm_features(self):
        """测试GLCM特征提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='gray')
        
        features = self.extractor.extract_glcm_features(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0

    def test_extract_lbp_features(self):
        """测试LBP特征提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='gray')
        
        features = self.extractor.extract_lbp_features(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0

    def test_extract_features(self):
        """测试完整特征提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='gray')
        
        features = self.extractor.extract_features(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0


class TestShapeExtractor:
    """形状特征提取器测试类"""

    def setup_method(self):
        """设置测试方法"""
        self.config = TraditionalFeatureConfig()
        self.extractor = ShapeFeatureExtractor(self.config)

    def test_init(self):
        """测试形状提取器初始化"""
        assert self.extractor is not None
        assert self.extractor.config == self.config

    def test_extract_contour_features(self):
        """测试轮廓特征提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='white')
        
        features = self.extractor.extract_contour_features(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0

    def test_extract_fourier_descriptors(self):
        """测试傅里叶描述符提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='white')
        
        features = self.extractor.extract_fourier_descriptors(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0

    def test_extract_features(self):
        """测试完整特征提取"""
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='white')
        
        features = self.extractor.extract_features(test_image)
        assert isinstance(features, np.ndarray)
        assert len(features) > 0 