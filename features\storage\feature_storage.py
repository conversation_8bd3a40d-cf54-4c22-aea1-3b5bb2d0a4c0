"""特征存储模块

提供特征数据的统一存储和检索接口，整合缓存、数据库和序列化功能。
"""

import pickle
import numpy as np
from typing import Optional, Dict, Any, List, Tuple, Union
from pathlib import Path
import logging
import threading
from datetime import datetime

from .feature_cache import FeatureCache
from .feature_serializer import FeatureSerializer, SerializationFormat


class FeatureStorage:
    """特征存储管理器
    
    提供特征数据的统一存储和检索接口，包括：
    - 内存缓存管理
    - 数据库特征操作
    - 特征序列化/反序列化
    - 批量操作支持
    """
    
    def __init__(self, fabric_repository, cache_size: int = 10000):
        """初始化特征存储管理器
        
        Args:
            fabric_repository: 数据库仓库对象
            cache_size: 缓存大小
        """
        self.fabric_repository = fabric_repository
        self.cache = FeatureCache(max_size=cache_size)
        self.serializer = FeatureSerializer()
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
    def get_features(self, image_id: Union[int, str]) -> Optional[np.ndarray]:
        """获取图像特征
        
        Args:
            image_id: 图像ID
            
        Returns:
            Optional[np.ndarray]: 特征数组
        """
        try:
            # 验证输入参数
            if image_id is None:
                self.logger.error("图像ID为空")
                return None
            
            # 先从缓存获取
            try:
                cached_features = self.cache.get(image_id)
                if cached_features is not None:
                    # 验证缓存的特征
                    if isinstance(cached_features, np.ndarray) and cached_features.size > 0:
                        # 检查异常值
                        if np.isnan(cached_features).any() or np.isinf(cached_features).any():
                            self.logger.warning(f"缓存的特征包含异常值，图像ID: {image_id}")
                            cached_features = np.nan_to_num(cached_features, nan=0.0, posinf=1.0, neginf=-1.0)
                        
                        if self.serializer.validate_features(cached_features):
                            self.logger.debug(f"从缓存获取特征成功，图像ID: {image_id}")
                            return cached_features
                        else:
                            self.logger.warning(f"缓存的特征验证失败，图像ID: {image_id}")
                            # 移除无效缓存
                            self.cache.remove(image_id)
                    else:
                        self.logger.warning(f"缓存的特征格式无效，图像ID: {image_id}")
                        # 移除无效缓存
                        self.cache.remove(image_id)
            except Exception as e:
                self.logger.warning(f"从缓存获取特征失败，图像ID: {image_id}, 错误: {e}")
            
            # 从数据库获取
            try:
                features = self._get_features_from_database(image_id)
                if features is not None:
                    # 验证从数据库获取的特征
                    if isinstance(features, np.ndarray) and features.size > 0:
                        # 检查异常值
                        if np.isnan(features).any() or np.isinf(features).any():
                            self.logger.warning(f"数据库的特征包含异常值，图像ID: {image_id}")
                            features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                        
                        # 确保特征是float32类型
                        if features.dtype != np.float32:
                            features = features.astype(np.float32)
                        
                        if self.serializer.validate_features(features):
                            # 更新缓存
                            try:
                                self.cache.put(image_id, features)
                            except Exception as e:
                                self.logger.warning(f"更新缓存失败，图像ID: {image_id}, 错误: {e}")
                            
                            self.logger.debug(f"从数据库获取特征成功，图像ID: {image_id}")
                            return features
                        else:
                            self.logger.warning(f"数据库的特征验证失败，图像ID: {image_id}")
                    else:
                        self.logger.warning(f"数据库的特征格式无效，图像ID: {image_id}")
                else:
                    self.logger.debug(f"数据库中未找到特征，图像ID: {image_id}")
            except Exception as e:
                self.logger.error(f"从数据库获取特征失败，图像ID: {image_id}, 错误: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取图像 {image_id} 的特征失败: {e}")
            return None
            
    def store_features(self, image_id: Union[int, str], features: np.ndarray,
                      metadata: Optional[Dict[str, Any]] = None) -> bool:
        """存储图像特征
        
        Args:
            image_id: 图像ID
            features: 特征数组
            metadata: 元数据
            
        Returns:
            bool: 是否成功存储
        """
        try:
            # 验证输入参数
            if image_id is None:
                self.logger.error("图像ID为空")
                return False
            
            if features is None:
                self.logger.error(f"图像 {image_id} 的特征为空")
                return False
            
            # 验证特征数组
            if not isinstance(features, np.ndarray):
                try:
                    features = np.array(features, dtype=np.float32)
                except Exception as e:
                    self.logger.error(f"图像 {image_id} 的特征无法转换为数组: {e}")
                    return False
            
            if features.size == 0:
                self.logger.error(f"图像 {image_id} 的特征数组为空")
                return False
            
            # 检查特征中的异常值
            if np.isnan(features).any() or np.isinf(features).any():
                self.logger.warning(f"图像 {image_id} 的特征包含异常值，进行清理")
                features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 确保特征是float32类型
            if features.dtype != np.float32:
                features = features.astype(np.float32)
            
            # 验证特征
            if not self.serializer.validate_features(features):
                self.logger.error(f"图像 {image_id} 的特征验证失败")
                return False
                
            # 存储到数据库
            if not self._store_features_to_database(image_id, features, metadata):
                self.logger.error(f"图像 {image_id} 的特征存储到数据库失败")
                return False
                
            # 更新缓存
            try:
                self.cache.put(image_id, features)
            except Exception as e:
                self.logger.warning(f"图像 {image_id} 的特征缓存更新失败: {e}")
                # 缓存失败不影响整体存储成功
            
            self.logger.debug(f"图像 {image_id} 的特征存储成功")
            return True
            
        except Exception as e:
            self.logger.error(f"存储图像 {image_id} 的特征失败: {e}")
            return False
            
    def batch_store_features(
        self, 
        features_data: List[Tuple[Union[int, str], np.ndarray, Optional[Dict[str, Any]]]]
    ) -> Dict[str, Any]:
        """批量存储特征
        
        Args:
            features_data: 特征数据列表，每个元素为(image_id, features, metadata)
            
        Returns:
            Dict[str, Any]: 存储结果统计
        """
        results = {
            'success_count': 0,
            'failed_count': 0,
            'failed_ids': [],
            'total_count': len(features_data)
        }
        
        try:
            for image_id, features, metadata in features_data:
                try:
                    if self.store_features(image_id, features, metadata):
                        results['success_count'] += 1
                    else:
                        results['failed_count'] += 1
                        results['failed_ids'].append(image_id)
                except Exception as e:
                    self.logger.error(f"批量存储图像 {image_id} 特征失败: {e}")
                    results['failed_count'] += 1
                    results['failed_ids'].append(image_id)
                    
            self.logger.info(f"批量存储完成: 成功 {results['success_count']}, 失败 {results['failed_count']}")
            return results
            
        except Exception as e:
            self.logger.error(f"批量存储特征失败: {e}")
            results['failed_count'] = results['total_count']
            return results
            
    def batch_get_features(
        self, 
        image_ids: List[Union[int, str]]
    ) -> Dict[Union[int, str], Optional[np.ndarray]]:
        """批量获取特征
        
        Args:
            image_ids: 图像ID列表
            
        Returns:
            Dict[Union[int, str], Optional[np.ndarray]]: 特征字典
        """
        results = {}
        
        try:
            for image_id in image_ids:
                results[image_id] = self.get_features(image_id)
                
            return results
            
        except Exception as e:
            self.logger.error(f"批量获取特征失败: {e}")
            return {image_id: None for image_id in image_ids}
            
    def remove_features(self, image_id: Union[int, str]) -> bool:
        """删除图像特征
        
        Args:
            image_id: 图像ID
            
        Returns:
            bool: 是否成功删除
        """
        try:
            # 从缓存删除
            self.cache.remove(image_id)
            
            # 从数据库删除
            return self._remove_features_from_database(image_id)
            
        except Exception as e:
            self.logger.error(f"删除图像 {image_id} 的特征失败: {e}")
            return False
            
    def get_all_features(self, include_inactive: bool = False) -> List[Tuple[Union[int, str], np.ndarray]]:
        """获取所有特征
        
        Args:
            include_inactive: 是否包含非活跃图像
            
        Returns:
            List[Tuple[Union[int, str], np.ndarray]]: 特征列表
        """
        try:
            # 从数据库获取所有图像
            fabric_images = self.fabric_repository.get_all(include_inactive=include_inactive)
            if not fabric_images:
                return []
                
            features_list = []
            for fabric_image in fabric_images:
                if not hasattr(fabric_image, 'id') or fabric_image.id is None:
                    continue
                    
                features = self.get_features(fabric_image.id)
                if features is not None:
                    features_list.append((fabric_image.id, features))
                    
            return features_list
            
        except Exception as e:
            self.logger.error(f"获取所有特征失败: {e}")
            return []
            
    def _get_features_from_database(self, image_id: Union[int, str]) -> Optional[np.ndarray]:
        """从数据库获取特征
        
        Args:
            image_id: 图像ID
            
        Returns:
            Optional[np.ndarray]: 特征数组
        """
        try:
            # 先尝试从 fabric_images 表获取
            fabric_image = self.fabric_repository.get_by_id(image_id)
            if fabric_image and hasattr(fabric_image, 'features') and fabric_image.features:
                try:
                    features = pickle.loads(fabric_image.features)
                    if self.serializer.validate_features(features):
                        return features
                except Exception as e:
                    self.logger.warning(f"从 fabric_images 表反序列化特征失败: {e}")
                    
            # 尝试从 image_features 表获取
            try:
                feature = self.fabric_repository.get_feature_by_image_id(image_id, "deep_features")
                if feature and hasattr(feature, 'feature_data') and feature.feature_data:
                    features = np.frombuffer(feature.feature_data, dtype=np.float32)
                    if self.serializer.validate_features(features):
                        return features
            except Exception as e:
                self.logger.warning(f"从 image_features 表获取特征失败: {e}")
                
            return None
            
        except Exception as e:
            self.logger.error(f"从数据库获取特征失败: {e}")
            return None
            
    def _store_features_to_database(self, image_id: Union[int, str], features: np.ndarray,
                                   metadata: Optional[Dict[str, Any]] = None) -> bool:
        """存储特征到数据库
        
        Args:
            image_id: 图像ID
            features: 特征数组
            metadata: 元数据
            
        Returns:
            bool: 是否成功存储
        """
        try:
            # 验证输入参数
            if image_id is None:
                raise ValueError("图像ID无效")
            
            if features is None or len(features) == 0:
                raise ValueError("特征向量为空")
            
            # 检查特征中的异常值
            if np.isnan(features).any() or np.isinf(features).any():
                self.logger.warning(f"特征包含NaN或无穷大值，图像ID: {image_id}")
                features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 序列化特征
            try:
                serialized_features = self.serializer.serialize_features(features)
                if serialized_features is None:
                    self.logger.error(f"特征序列化失败，图像ID: {image_id}")
                    return False
            except Exception as e:
                self.logger.error(f"序列化特征失败，图像ID: {image_id}, 错误: {e}")
                return False
                
            # 更新 fabric_images 表
            try:
                fabric_image = self.fabric_repository.get_by_id(image_id)
                if fabric_image:
                    fabric_image.features = serialized_features
                    fabric_image.updated_at = datetime.now()
                    self.fabric_repository.update(fabric_image)
                    self.logger.debug(f"更新fabric_images表成功，图像ID: {image_id}")
            except Exception as e:
                self.logger.warning(f"更新 fabric_images 表失败: {e}")
                
            # 更新 image_features 表
            try:
                feature_data = features.astype(np.float32).tobytes()
                # 从metadata中提取model_name，如果存在的话
                model_name = None
                if metadata and isinstance(metadata, dict) and 'model_name' in metadata:
                    model_name = metadata['model_name']
                
                self.fabric_repository.update_or_create_feature(
                    image_id=image_id,
                    feature_type="deep_features",
                    feature_data=feature_data,
                    model_name=model_name
                )
                self.logger.debug(f"更新image_features表成功，图像ID: {image_id}")
            except Exception as e:
                self.logger.warning(f"更新 image_features 表失败: {e}")
                
            return True
            
        except Exception as e:
            self.logger.error(f"存储特征到数据库失败: {e}")
            return False
            
    def _remove_features_from_database(self, image_id: Union[int, str]) -> bool:
        """从数据库删除特征
        
        Args:
            image_id: 图像ID
            
        Returns:
            bool: 是否成功删除
        """
        try:
            # 清除 fabric_images 表中的特征
            try:
                fabric_image = self.fabric_repository.get_by_id(image_id)
                if fabric_image:
                    fabric_image.features = None
                    fabric_image.updated_at = datetime.now()
                    self.fabric_repository.update(fabric_image)
            except Exception as e:
                self.logger.warning(f"清除 fabric_images 表特征失败: {e}")
                
            # 删除 image_features 表中的记录
            try:
                self.fabric_repository.delete_feature_by_image_id(image_id, "deep_features")
            except Exception as e:
                self.logger.warning(f"删除 image_features 表记录失败: {e}")
                
            return True
            
        except Exception as e:
            self.logger.error(f"从数据库删除特征失败: {e}")
            return False
            
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.logger.info("特征缓存已清空")
        
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache.get_statistics()
        
    def save_cache_to_disk(self, file_path: Path) -> bool:
        """保存缓存到磁盘"""
        return self.cache.save_to_disk(file_path)
        
    def load_cache_from_disk(self, file_path: Path) -> bool:
        """从磁盘加载缓存"""
        return self.cache.load_from_disk(file_path)
        
    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            cache_stats = self.get_cache_statistics()
            
            # 获取数据库统计
            db_stats = self.fabric_repository.get_statistics() if hasattr(self.fabric_repository, 'get_statistics') else {}
            
            return {
                'cache': cache_stats,
                'database': db_stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取存储统计信息失败: {e}")
            return {}
            
    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.clear_cache()
            self.logger.debug("特征存储资源清理完成")
        except Exception as e:
            self.logger.error(f"特征存储资源清理失败: {e}")