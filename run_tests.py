#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本

运行所有测试并生成详细报告。
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_command(command, description):
    """运行命令并返回结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    start_time = time.time()
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        end_time = time.time()
        
        print(f"执行时间: {end_time - start_time:.2f}秒")
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode,
            'duration': end_time - start_time
        }
    except Exception as e:
        print(f"执行失败: {e}")
        return {
            'success': False,
            'stdout': '',
            'stderr': str(e),
            'returncode': -1,
            'duration': 0
        }


def run_unit_tests():
    """运行单元测试"""
    return run_command(
        "python -m pytest tests/unit/ -v --tb=short --cov=features --cov=search --cov=config --cov-report=html:reports/coverage --cov-report=term-missing",
        "单元测试"
    )


def run_integration_tests():
    """运行集成测试"""
    return run_command(
        "python -m pytest tests/integration/ -v --tb=short",
        "集成测试"
    )


def run_performance_tests():
    """运行性能测试"""
    return run_command(
        "python -m pytest tests/performance/ -v --tb=short",
        "性能测试"
    )


def run_api_tests():
    """运行API测试"""
    return run_command(
        "python -m pytest tests/integration/test_api_integration.py -v --tb=short",
        "API测试"
    )


def run_all_tests():
    """运行所有测试"""
    return run_command(
        "python -m pytest tests/ -v --tb=short --cov=features --cov=search --cov=config --cov-report=html:reports/coverage --cov-report=term-missing",
        "所有测试"
    )


def generate_test_report(results):
    """生成测试报告"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'success_rate': 0.0
        },
        'details': results,
        'recommendations': []
    }
    
    # 分析结果
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for test_type, result in results.items():
        if result['success']:
            passed_tests += 1
        else:
            failed_tests += 1
            report['recommendations'].append(f"{test_type}测试失败，需要检查")
        total_tests += 1
    
    report['summary']['total_tests'] = total_tests
    report['summary']['passed'] = passed_tests
    report['summary']['failed'] = failed_tests
    report['summary']['success_rate'] = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    return report


def save_report(report, filename):
    """保存报告到文件"""
    reports_dir = project_root / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    report_path = reports_dir / filename
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n报告已保存到: {report_path}")


def print_summary(report):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("测试摘要")
    print("="*60)
    
    summary = report['summary']
    print(f"总测试数: {summary['total_tests']}")
    print(f"通过: {summary['passed']}")
    print(f"失败: {summary['failed']}")
    print(f"成功率: {summary['success_rate']:.1f}%")
    
    if report['recommendations']:
        print("\n建议:")
        for rec in report['recommendations']:
            print(f"- {rec}")


def main():
    """主函数"""
    print("Fabric Search v2 - 测试套件")
    print("="*60)
    
    # 检查依赖
    print("检查测试依赖...")
    try:
        import pytest
        import coverage
        print("✓ pytest 和 coverage 已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install pytest pytest-cov")
        return
    
    # 运行测试
    results = {}
    
    # 单元测试
    results['unit_tests'] = run_unit_tests()
    
    # 集成测试
    results['integration_tests'] = run_integration_tests()
    
    # 性能测试
    results['performance_tests'] = run_performance_tests()
    
    # API测试
    results['api_tests'] = run_api_tests()
    
    # 生成报告
    report = generate_test_report(results)
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_report(report, f"test_report_{timestamp}.json")
    
    # 打印摘要
    print_summary(report)
    
    # 返回状态码
    if report['summary']['failed'] > 0:
        sys.exit(1)
    else:
        print("\n✓ 所有测试通过!")
        sys.exit(0)


if __name__ == "__main__":
    main() 