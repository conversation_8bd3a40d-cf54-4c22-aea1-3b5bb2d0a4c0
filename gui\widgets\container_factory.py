#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容器工厂模块

该模块提供各种容器控件的创建功能。
"""

from typing import List
from PyQt6.QtWidgets import (
    QWidget, QGroupBox, QFrame, QTabWidget, QSplitter, QScrollArea,
    QStackedWidget, QProgressBar, QListWidget, QTreeWidget, QTableWidget
)
from PyQt6.QtCore import Qt

from utils.log_utils import LoggerMixin
from .base import WidgetConfig


class ContainerFactory(LoggerMixin):
    """容器工厂"""
    
    def __init__(self):
        super().__init__()
    
    def create_group_box(self, title: str = "", layout_type: str = None) -> QGroupBox:
        """创建分组框
        
        Args:
            title: 分组框标题
            layout_type: 布局类型 ("vbox", "hbox", "grid", "form")
            
        Returns:
            QGroupBox: 分组框控件
        """
        try:
            group_box = QGroupBox(title)
            
            # 创建布局
            if layout_type:
                if layout_type.lower() == "vbox":
                    layout = QVBoxLayout(group_box)
                elif layout_type.lower() == "hbox":
                    layout = QHBoxLayout(group_box)
                elif layout_type.lower() == "grid":
                    layout = QGridLayout(group_box)
                elif layout_type.lower() == "form":
                    layout = QFormLayout(group_box)
                else:
                    layout = QVBoxLayout(group_box)
                    
                # 设置布局边距
                layout.setContentsMargins(5, 10, 5, 5)
                layout.setSpacing(5)
            
            return group_box
            
        except Exception as e:
            self.logger.error(f"创建分组框失败: {e}")
            return QGroupBox(title)
    
    def create_frame(self, frame_style: QFrame.Shape = QFrame.Shape.StyledPanel,
                    shadow: QFrame.Shadow = QFrame.Shadow.Raised,
                    config: WidgetConfig = None) -> QFrame:
        """创建框架
        
        Args:
            frame_style: 框架样式
            shadow: 阴影样式
            config: 控件配置
            
        Returns:
            QFrame: 框架控件
        """
        try:
            frame = QFrame()
            frame.setFrameShape(frame_style)
            frame.setFrameShadow(shadow)
            
            # 应用配置
            if config:
                config.apply_to_widget(frame)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"创建框架失败: {e}")
            return QFrame()
    
    def create_tab_widget(self, config: WidgetConfig = None) -> QTabWidget:
        """创建标签页控件
        
        Args:
            config: 控件配置
            
        Returns:
            QTabWidget: 标签页控件
        """
        try:
            tab_widget = QTabWidget()
            tab_widget.setTabPosition(QTabWidget.TabPosition.North)
            tab_widget.setTabsClosable(False)
            tab_widget.setMovable(True)
            
            # 应用配置
            if config:
                config.apply_to_widget(tab_widget)
            
            return tab_widget
            
        except Exception as e:
            self.logger.error(f"创建标签页控件失败: {e}")
            return QTabWidget()
    
    def create_splitter(self, orientation: Qt.Orientation = Qt.Orientation.Horizontal,
                       widgets: List[QWidget] = None,
                       config: WidgetConfig = None) -> QSplitter:
        """创建分割器
        
        Args:
            orientation: 方向
            widgets: 控件列表
            config: 控件配置
            
        Returns:
            QSplitter: 分割器控件
        """
        try:
            splitter = QSplitter(orientation)
            
            # 添加控件
            if widgets:
                for widget in widgets:
                    if widget:
                        splitter.addWidget(widget)
            
            # 应用配置
            if config:
                config.apply_to_widget(splitter)
            
            return splitter
            
        except Exception as e:
            self.logger.error(f"创建分割器失败: {e}")
            return QSplitter(orientation)
    
    def create_scroll_area(self, widget: QWidget = None,
                          horizontal_policy: Qt.ScrollBarPolicy = Qt.ScrollBarPolicy.ScrollBarAsNeeded,
                          vertical_policy: Qt.ScrollBarPolicy = Qt.ScrollBarPolicy.ScrollBarAsNeeded,
                          config: WidgetConfig = None) -> QScrollArea:
        """创建滚动区域
        
        Args:
            widget: 要包装的控件
            horizontal_policy: 水平滚动条策略
            vertical_policy: 垂直滚动条策略
            config: 控件配置
            
        Returns:
            QScrollArea: 滚动区域控件
        """
        try:
            scroll_area = QScrollArea()
            
            if widget:
                scroll_area.setWidget(widget)
            
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(horizontal_policy)
            scroll_area.setVerticalScrollBarPolicy(vertical_policy)
            
            # 应用配置
            if config:
                config.apply_to_widget(scroll_area)
            
            return scroll_area
            
        except Exception as e:
            self.logger.error(f"创建滚动区域失败: {e}")
            return QScrollArea()
    
    def create_stacked_widget(self, config: WidgetConfig = None) -> QStackedWidget:
        """创建堆叠控件
        
        Args:
            config: 控件配置
            
        Returns:
            QStackedWidget: 堆叠控件
        """
        try:
            stacked_widget = QStackedWidget()
            
            # 应用配置
            if config:
                config.apply_to_widget(stacked_widget)
            
            return stacked_widget
            
        except Exception as e:
            self.logger.error(f"创建堆叠控件失败: {e}")
            return QStackedWidget()
    
    def create_progress_bar(self, minimum: int = 0, maximum: int = 100,
                           value: int = 0, text_visible: bool = True,
                           config: WidgetConfig = None) -> QProgressBar:
        """创建进度条
        
        Args:
            minimum: 最小值
            maximum: 最大值
            value: 当前值
            text_visible: 是否显示文本
            config: 控件配置
            
        Returns:
            QProgressBar: 进度条控件
        """
        try:
            progress_bar = QProgressBar()
            
            progress_bar.setMinimum(minimum)
            progress_bar.setMaximum(maximum)
            progress_bar.setValue(value)
            progress_bar.setTextVisible(text_visible)
            
            # 应用配置
            if config:
                config.apply_to_widget(progress_bar)
            
            return progress_bar
            
        except Exception as e:
            self.logger.error(f"创建进度条失败: {e}")
            return QProgressBar()
    
    def create_list_widget(self, items: List[str] = None,
                          config: WidgetConfig = None) -> QListWidget:
        """创建列表控件
        
        Args:
            items: 列表项
            config: 控件配置
            
        Returns:
            QListWidget: 列表控件
        """
        try:
            list_widget = QListWidget()
            
            # 添加项目
            if items:
                list_widget.addItems(items)
            
            # 应用配置
            if config:
                config.apply_to_widget(list_widget)
            
            return list_widget
            
        except Exception as e:
            self.logger.error(f"创建列表控件失败: {e}")
            return QListWidget()
    
    def create_tree_widget(self, headers: List[str] = None,
                          config: WidgetConfig = None) -> QTreeWidget:
        """创建树形控件
        
        Args:
            headers: 表头列表
            config: 控件配置
            
        Returns:
            QTreeWidget: 树形控件
        """
        try:
            tree_widget = QTreeWidget()
            
            # 设置表头
            if headers:
                tree_widget.setHeaderLabels(headers)
            
            # 应用配置
            if config:
                config.apply_to_widget(tree_widget)
            
            return tree_widget
            
        except Exception as e:
            self.logger.error(f"创建树形控件失败: {e}")
            return QTreeWidget()
    
    def create_table_widget(self, rows: int = 0, columns: int = 0,
                           headers: List[str] = None,
                           config: WidgetConfig = None) -> QTableWidget:
        """创建表格控件
        
        Args:
            rows: 行数
            columns: 列数
            headers: 表头列表
            config: 控件配置
            
        Returns:
            QTableWidget: 表格控件
        """
        try:
            table_widget = QTableWidget(rows, columns)
            
            # 设置表头
            if headers:
                table_widget.setHorizontalHeaderLabels(headers)
            
            # 应用配置
            if config:
                config.apply_to_widget(table_widget)
            
            return table_widget
            
        except Exception as e:
            self.logger.error(f"创建表格控件失败: {e}")
            return QTableWidget(rows, columns)
    
    def create_separator(self, orientation: Qt.Orientation = Qt.Orientation.Horizontal,
                        config: WidgetConfig = None) -> QFrame:
        """创建分隔线
        
        Args:
            orientation: 方向
            config: 控件配置
            
        Returns:
            QFrame: 分隔线控件
        """
        try:
            separator = QFrame()
            
            if orientation == Qt.Orientation.Horizontal:
                separator.setFrameShape(QFrame.Shape.HLine)
            else:
                separator.setFrameShape(QFrame.Shape.VLine)
                
            separator.setFrameShadow(QFrame.Shadow.Sunken)
            
            # 应用配置
            if config:
                config.apply_to_widget(separator)
            
            return separator
            
        except Exception as e:
            self.logger.error(f"创建分隔线失败: {e}")
            return QFrame()