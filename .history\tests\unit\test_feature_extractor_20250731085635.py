#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取器单元测试

测试特征提取器类的基本功能。
"""

import numpy as np
from PIL import Image
from unittest.mock import Mock

from features.core.feature_extractor import FeatureExtractor


class TestFeatureExtractor:
    """特征提取器测试类"""

    def test_init(self):
        """测试特征提取器初始化"""
        extractor = FeatureExtractor()
        assert extractor is not None
        assert hasattr(extractor, 'extractors')

    def test_register_extractor(self):
        """测试提取器注册"""
        extractor = FeatureExtractor()
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        
        extractor.register_extractor(mock_extractor)
        assert 'test_extractor' in extractor.extractors

    def test_extract_features(self):
        """测试特征提取"""
        extractor = FeatureExtractor()
        
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # 模拟提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.extract_features.return_value = np.array([1, 2, 3])
        
        extractor.register_extractor(mock_extractor)
        
        features = extractor.extract_features(test_image)
        assert isinstance(features, dict)
        assert 'test_extractor' in features

    def test_batch_extract_features(self):
        """测试批量特征提取"""
        extractor = FeatureExtractor()
        
        # 创建测试图像列表
        test_images = [
            Image.new('RGB', (100, 100), color='red'),
            Image.new('RGB', (100, 100), color='blue')
        ]
        
        # 模拟提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.extract_features.return_value = np.array([1, 2, 3])
        
        extractor.register_extractor(mock_extractor)
        
        features_list = extractor.batch_extract_features(test_images)
        assert len(features_list) == 2
        assert all(isinstance(f, dict) for f in features_list)

    def test_extract_invalid_image(self):
        """测试无效图像处理"""
        extractor = FeatureExtractor()
        
        # 测试None图像
        features = extractor.extract_features(None)
        assert features == {}

        # 测试非图像对象
        features = extractor.extract_features("not_an_image")
        assert features == {}

    def test_get_extractor_info(self):
        """测试获取提取器信息"""
        extractor = FeatureExtractor()
        
        # 模拟提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.get_info.return_value = {'type': 'test'}
        
        extractor.register_extractor(mock_extractor)
        
        info = extractor.get_extractor_info('test_extractor')
        assert info == {'type': 'test'}

    def test_basic_functionality(self):
        """测试基本功能"""
        extractor = FeatureExtractor()
        assert extractor is not None
        assert hasattr(extractor, 'extractors')
        assert isinstance(extractor.extractors, dict)

    def test_empty_extractors(self):
        """测试空提取器列表"""
        extractor = FeatureExtractor()
        features = extractor.extract_features(Image.new('RGB', (100, 100)))
        assert features == {}

    def test_multiple_extractors(self):
        """测试多个提取器"""
        extractor = FeatureExtractor()
        
        # 创建多个模拟提取器
        mock_extractor1 = Mock()
        mock_extractor1.name = 'extractor1'
        mock_extractor1.extract_features.return_value = np.array([1, 2, 3])
        
        mock_extractor2 = Mock()
        mock_extractor2.name = 'extractor2'
        mock_extractor2.extract_features.return_value = np.array([4, 5, 6])
        
        extractor.register_extractor(mock_extractor1)
        extractor.register_extractor(mock_extractor2)
        
        test_image = Image.new('RGB', (100, 100), color='red')
        features = extractor.extract_features(test_image)
        
        assert 'extractor1' in features
        assert 'extractor2' in features
        assert len(features) == 2 