"""批处理器模块

提供特征提取的批处理功能，支持多线程处理和进度监控。
"""

import threading
import time
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable, Union
from pathlib import Path
import logging
from datetime import datetime

from .batch_task import BatchTask, BatchTaskItem, BatchTaskStatus
from .progress_monitor import ProgressMonitor
from features.storage.feature_storage import FeatureStorage


class BatchProcessor:
    """批处理器
    
    提供特征提取的批处理功能，包括：
    - 多线程并行处理
    - 错误处理和重试
    - 进度监控
    - 任务管理
    """
    
    def __init__(self, feature_extractor, feature_storage: FeatureStorage,
                 max_workers: int = 4, progress_monitor: Optional[ProgressMonitor] = None):
        """初始化批处理器
        
        Args:
            feature_extractor: 特征提取器
            feature_storage: 特征存储器
            max_workers: 最大工作线程数
            progress_monitor: 进度监控器
        """
        self.feature_extractor = feature_extractor
        self.feature_storage = feature_storage
        self.max_workers = max_workers
        self.progress_monitor = progress_monitor or ProgressMonitor(update_interval=0.2)
        self.logger = logging.getLogger(__name__)
        
        # 任务管理
        self._active_tasks: Dict[str, BatchTask] = {}
        self._task_lock = threading.RLock()
        
        # 执行器
        self._executor: Optional[ThreadPoolExecutor] = None
        
    def shutdown(self):
        """关闭批处理器，清理资源
        
        在FeatureManager的cleanup方法中被调用
        """
        # 调用cleanup方法，避免代码重复
        self.cleanup()
        self.logger.info("批处理器已关闭")
        
    def create_task(self, name: str, image_paths: List[Union[str, Path]],
                   description: str = "", **kwargs) -> BatchTask:
        """创建批处理任务
        
        Args:
            name: 任务名称
            image_paths: 图像路径列表
            description: 任务描述
            **kwargs: 其他任务配置参数
            
        Returns:
            BatchTask: 创建的任务
        """
        # 创建任务项
        items = []
        for i, path in enumerate(image_paths):
            item = BatchTaskItem(
                item_id=f"{name}_{i}",
                file_path=Path(path)
            )
            items.append(item)
            
        # 创建任务
        task = BatchTask(
            name=name,
            description=description,
            items=items,
            max_workers=kwargs.get('max_workers', self.max_workers),
            batch_size=kwargs.get('batch_size', 32),
            max_retries=kwargs.get('max_retries', 3),
            timeout_seconds=kwargs.get('timeout_seconds', 300)
        )
        
        # 添加到活跃任务列表
        with self._task_lock:
            self._active_tasks[task.task_id] = task
            
        self.logger.info(f"创建批处理任务: {name} (共 {len(image_paths)} 个项目)")
        return task
        
    def submit_task(self, task: BatchTask) -> bool:
        """提交任务执行
        
        Args:
            task: 批处理任务
            
        Returns:
            bool: 是否成功提交
        """
        try:
            # 验证任务
            if not task.items:
                self.logger.error("任务没有项目")
                return False
                
            # 添加到监控器
            self.progress_monitor.add_task(task)
            
            # 启动处理线程
            processing_thread = threading.Thread(
                target=self._process_task,
                args=(task,),
                daemon=True
            )
            processing_thread.start()
            
            self.logger.info(f"提交批处理任务: {task.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"提交任务失败: {e}")
            return False
            
    def _process_task(self, task: BatchTask) -> None:
        """处理任务
        
        Args:
            task: 批处理任务
        """
        try:
            task.start()
            self.logger.info(f"开始处理任务: {task.name}")
            
            # 创建线程池
            with ThreadPoolExecutor(max_workers=task.max_workers) as executor:
                self._executor = executor
                
                # 提交所有任务项
                future_to_item = {}
                for item in task.items:
                    if item.status == BatchTaskStatus.PENDING:
                        future = executor.submit(self._process_item, task, item)
                        future_to_item[future] = item
                        
                # 等待完成
                try:
                    # 使用as_completed但捕获超时异常
                    for future in as_completed(future_to_item, timeout=task.timeout_seconds):
                        item = future_to_item[future]
                        try:
                            success = future.result()
                            if success:
                                item.status = BatchTaskStatus.COMPLETED
                                item.features_extracted = True
                            else:
                                item.status = BatchTaskStatus.FAILED
                                if item.retry_count < task.max_retries:
                                    # 重试，但确保线程池仍然可用
                                    try:
                                        item.retry_count += 1
                                        item.status = BatchTaskStatus.PENDING
                                        retry_future = executor.submit(self._process_item, task, item)
                                        future_to_item[retry_future] = item
                                    except RuntimeError as re:
                                        # 线程池已关闭，无法提交新任务
                                        self.logger.warning(f"无法重试任务 {item.item_id}，线程池已关闭: {re}")
                                        item.status = BatchTaskStatus.FAILED
                                        item.error_message = f"无法重试: {str(re)}"
                                    
                        except Exception as e:
                            self.logger.error(f"处理项目 {item.item_id} 失败: {e}")
                            item.status = BatchTaskStatus.FAILED
                            item.error_message = str(e)
                except concurrent.futures.TimeoutError:
                    # 超时后，取消所有未完成的任务
                    self.logger.warning(f"任务 {task.name} 处理超时，取消剩余任务")
                    for future, item in future_to_item.items():
                        if not future.done():
                            future.cancel()
                            item.status = BatchTaskStatus.FAILED
                            item.error_message = "任务处理超时"
                
                # 确保所有任务都已完成或被取消
                for future, item in list(future_to_item.items()):
                    if not future.done() and not future.cancelled():
                        try:
                            # 尝试取消任务
                            future.cancel()
                            item.status = BatchTaskStatus.FAILED
                            item.error_message = "任务被取消"
                        except Exception as e:
                            self.logger.error(f"取消任务 {item.item_id} 失败: {e}")
                            item.status = BatchTaskStatus.FAILED
                            item.error_message = f"取消任务失败: {str(e)}"
                        
                self._executor = None
                
            # 完成任务
            task.update_progress()
            if task.failed_items == 0:
                task.complete()
            else:
                task.fail(f"有 {task.failed_items} 个项目处理失败")
                
            self.logger.info(f"任务处理完成: {task.name} (成功: {task.successful_items}, 失败: {task.failed_items})")
            
        except Exception as e:
            self.logger.error(f"处理任务失败: {e}")
            task.fail(str(e))
            
    def _process_item(self, task: BatchTask, item: BatchTaskItem) -> bool:
        """处理单个任务项
        
        Args:
            task: 批处理任务
            item: 任务项
            
        Returns:
            bool: 是否成功处理
        """
        start_time = time.time()
        
        try:
            # 验证输入参数
            if not item or not item.file_path:
                item.error_message = "任务项或文件路径无效"
                return False
            
            # 确保file_path是Path对象
            if isinstance(item.file_path, str):
                item.file_path = Path(item.file_path)
            
            # 检查文件是否存在
            if not item.file_path.exists():
                item.error_message = f"文件不存在: {item.file_path}"
                return False
            
            # 检查文件是否可读
            if not item.file_path.is_file():
                item.error_message = f"路径不是文件: {item.file_path}"
                return False
            
            # 检查文件大小
            try:
                file_size = item.file_path.stat().st_size
                if file_size == 0:
                    item.error_message = f"文件为空: {item.file_path}"
                    return False
                
                if file_size > 100 * 1024 * 1024:  # 100MB限制
                    item.error_message = f"文件过大: {item.file_path}, 大小: {file_size / 1024 / 1024:.1f}MB"
                    return False
            except OSError as e:
                item.error_message = f"无法获取文件信息: {item.file_path}, 错误: {e}"
                return False
            
            # 检查任务是否被取消
            if task.status == BatchTaskStatus.CANCELLED:
                item.error_message = "任务已被取消"
                return False
            
            # 提取特征
            try:
                result = self.feature_extractor.extract_features(str(item.file_path))
                
                if not result:
                    item.error_message = "特征提取返回空结果"
                    return False
                
                if not result.success:
                    item.error_message = f"特征提取失败: {getattr(result, 'error_message', '未知错误')}"
                    return False
                
                if result.features is None or len(result.features) == 0:
                    item.error_message = "提取的特征为空"
                    return False
                
                # 检查特征中的异常值
                import numpy as np
                if np.isnan(result.features).any() or np.isinf(result.features).any():
                    self.logger.warning(f"特征包含异常值，文件: {item.file_path}")
                    result.features = np.nan_to_num(result.features, nan=0.0, posinf=1.0, neginf=-1.0)
                
            except Exception as e:
                item.error_message = f"特征提取异常: {str(e)}"
                return False
                
            # 存储特征
            try:
                # 获取图像信息（安全方式）
                image_info = None
                file_info = None
                
                try:
                    from utils.image_utils import ImageProcessor
                    image_processor = ImageProcessor()
                    image_info = image_processor.get_image_info(str(item.file_path))
                except Exception as e:
                    self.logger.warning(f"获取图像信息失败: {item.file_path}, 错误: {e}")
                
                try:
                    from utils.file_utils import FileManager
                    file_manager = FileManager()
                    file_info = file_manager.get_file_info(str(item.file_path))
                except Exception as e:
                    self.logger.warning(f"获取文件信息失败: {item.file_path}, 错误: {e}")
                
                # 获取或创建图像记录
                fabric_image = None
                image_id = None
                
                try:
                    fabric_repository = self.feature_storage.fabric_repository
                    fabric_image = fabric_repository.get_by_path(str(item.file_path))
                    
                    if not fabric_image:
                        # 创建新记录
                        fabric_image = fabric_repository.create_fabric_image(str(item.file_path))
                        if not fabric_image:
                            # 如果创建失败，使用文件路径作为ID
                            self.logger.warning(f"无法创建图像记录，使用文件路径作为ID: {item.file_path}")
                            image_id = str(item.file_path)
                        else:
                            image_id = fabric_image.id
                    else:
                        image_id = fabric_image.id
                        
                except Exception as e:
                    self.logger.warning(f"处理图像记录失败，使用文件路径作为ID: {item.file_path}, 错误: {e}")
                    image_id = str(item.file_path)
                
                # 准备元数据
                metadata = {
                    'file_path': str(item.file_path),
                    'extraction_time': datetime.now().isoformat(),
                    'model_name': getattr(result, 'model_name', 'unknown'),
                    'feature_type': getattr(result, 'feature_type', 'deep_features'),
                    'file_size': file_size if 'file_size' in locals() else None,
                    'processing_time': None  # 将在最后设置
                }
                
                # 添加图像信息到元数据
                if image_info:
                    if isinstance(image_info, dict):
                        metadata.update({
                            'image_width': image_info.get('width'),
                            'image_height': image_info.get('height'),
                            'image_mode': image_info.get('mode'),
                            'image_format': image_info.get('format')
                        })
                    else:
                        # 假设image_info是一个对象
                        metadata.update({
                            'image_width': getattr(image_info, 'width', None),
                            'image_height': getattr(image_info, 'height', None),
                            'image_mode': getattr(image_info, 'mode', None),
                            'image_format': getattr(image_info, 'format', None)
                        })
                
                # 添加文件信息到元数据
                if file_info:
                    if isinstance(file_info, dict):
                        metadata.update({
                            'file_modified_time': file_info.get('modified_time'),
                            'file_created_time': file_info.get('created_time')
                        })
                    else:
                        # 假设file_info是一个对象
                        metadata.update({
                            'file_modified_time': getattr(file_info, 'modified_time', None),
                            'file_created_time': getattr(file_info, 'created_time', None)
                        })
                
                # 合并任务项的元数据
                if item.metadata:
                    metadata.update(item.metadata)
                
                # 存储特征
                if image_id:
                    success = self.feature_storage.store_features(image_id, result.features, metadata)
                    if not success:
                        # 尝试使用简化的存储方式
                        try:
                            success = self.feature_storage.store_features(str(item.file_path), result.features)
                            if not success:
                                item.error_message = "特征存储失败"
                                return False
                        except Exception as store_e:
                            item.error_message = f"特征存储异常: {str(store_e)}"
                            return False
                else:
                    item.error_message = "无法确定图像ID"
                    return False
                    
            except Exception as e:
                item.error_message = f"存储特征时发生异常: {str(e)}"
                return False
                
            # 记录处理时间
            processing_time = time.time() - start_time
            item.processing_time = processing_time
            
            # 更新元数据中的处理时间
            if 'metadata' in locals() and metadata:
                metadata['processing_time'] = processing_time
            
            # 标记特征已提取
            item.features_extracted = True
            
            self.logger.debug(f"成功处理任务项: {item.file_path}, 耗时: {processing_time:.2f}秒")
            return True
            
        except Exception as e:
            processing_time = time.time() - start_time
            item.error_message = f"处理任务项时发生未知错误: {str(e)}"
            item.processing_time = processing_time
            self.logger.error(f"处理任务项失败: {item.file_path}, 错误: {e}, 耗时: {processing_time:.2f}秒")
            return False
        finally:
            # 确保处理时间被记录
            if not hasattr(item, 'processing_time') or item.processing_time is None:
                item.processing_time = time.time() - start_time
            
    def get_task(self, task_id: str) -> Optional[BatchTask]:
        """获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[BatchTask]: 任务对象
        """
        with self._task_lock:
            return self._active_tasks.get(task_id)
            
    def get_all_tasks(self) -> List[BatchTask]:
        """获取所有任务
        
        Returns:
            List[BatchTask]: 任务列表
        """
        with self._task_lock:
            return list(self._active_tasks.values())
            
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            with self._task_lock:
                task = self._active_tasks.get(task_id)
                if not task:
                    return False
                    
                task.cancel()
                
                # 如果有执行器在运行，尝试停止
                if self._executor:
                    # 注意：ThreadPoolExecutor 没有直接的取消方法
                    # 这里只是标记任务为取消状态
                    pass
                    
            self.logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return False
            
    def pause_task(self, task_id: str) -> bool:
        """暂停任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        try:
            with self._task_lock:
                task = self._active_tasks.get(task_id)
                if not task:
                    return False
                    
                task.pause()
                
            self.logger.info(f"任务已暂停: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"暂停任务失败: {e}")
            return False
            
    def resume_task(self, task_id: str) -> bool:
        """恢复任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        try:
            with self._task_lock:
                task = self._active_tasks.get(task_id)
                if not task:
                    return False
                    
                task.resume()
                
            self.logger.info(f"任务已恢复: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复任务失败: {e}")
            return False
            
    def remove_task(self, task_id: str) -> bool:
        """移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功移除
        """
        try:
            with self._task_lock:
                if task_id in self._active_tasks:
                    task = self._active_tasks.pop(task_id)
                    self.progress_monitor.remove_task(task_id)
                    self.logger.info(f"任务已移除: {task_id}")
                    return True
            return False
            
        except Exception as e:
            self.logger.error(f"移除任务失败: {e}")
            return False
            
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._task_lock:
            total_tasks = len(self._active_tasks)
            running_tasks = sum(1 for task in self._active_tasks.values() 
                              if task.status == BatchTaskStatus.RUNNING)
            completed_tasks = sum(1 for task in self._active_tasks.values() 
                                if task.status == BatchTaskStatus.COMPLETED)
            failed_tasks = sum(1 for task in self._active_tasks.values() 
                             if task.status == BatchTaskStatus.FAILED)
            
            total_items = sum(task.total_items for task in self._active_tasks.values())
            processed_items = sum(task.processed_items for task in self._active_tasks.values())
            successful_items = sum(task.successful_items for task in self._active_tasks.values())
            
            return {
                'total_tasks': total_tasks,
                'running_tasks': running_tasks,
                'completed_tasks': completed_tasks,
                'failed_tasks': failed_tasks,
                'total_items': total_items,
                'processed_items': processed_items,
                'successful_items': successful_items,
                'success_rate': (successful_items / processed_items * 100) if processed_items > 0 else 0,
                'performance_stats': self.progress_monitor.get_performance_stats()
            }
            
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 停止所有任务
            with self._task_lock:
                for task in self._active_tasks.values():
                    if task.status == BatchTaskStatus.RUNNING:
                        task.cancel()
                        
            # 停止进度监控器
            self.progress_monitor.cleanup()
            
            # 清空任务列表
            with self._task_lock:
                self._active_tasks.clear()
                
            self.logger.debug("批处理器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"批处理器资源清理失败: {e}")