@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    Fabric Search - 图像搜索工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 显示Python版本
echo [信息] 检测到Python版本:
for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo         %%i
echo.

:: 检查虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo [信息] 发现虚拟环境，正在激活...
    call venv\Scripts\activate.bat
    echo [信息] 虚拟环境已激活
) else (
    echo [警告] 未找到虚拟环境
    echo [建议] 创建虚拟环境以避免依赖冲突:
    echo         python -m venv venv
    echo         venv\Scripts\activate
    echo         pip install -r requirements.txt
    echo.
    set /p choice="是否继续使用系统Python环境? (y/N): "
    if /i not "!choice!"=="y" (
        echo [信息] 用户取消操作
        pause
        exit /b 0
    )
)

:: 检查依赖
echo [信息] 检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo [错误] PyQt6未安装
    echo [解决] 请运行: pip install -r requirements.txt
    pause
    exit /b 1
)

echo [信息] 依赖检查通过
echo.

:: 创建必要目录
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "cache" mkdir cache
if not exist "models" mkdir models
if not exist "exports" mkdir exports
if not exist "temp" mkdir temp

echo [信息] 正在启动Fabric Search...
echo.

:: 启动应用程序
python main.py --mode gui

:: 检查退出码
if errorlevel 1 (
    echo.
    echo [错误] 应用程序启动失败
    echo [建议] 检查日志文件: logs\app.log
    pause
) else (
    echo.
    echo [信息] 应用程序正常退出
)

endlocal