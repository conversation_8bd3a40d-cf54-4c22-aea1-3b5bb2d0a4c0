"""特征配置模块

定义特征提取相关的配置参数。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum


class FeatureType(Enum):
    """特征类型枚举"""
    DEEP_FEATURES = "deep_features"
    COLOR_HISTOGRAM = "color_histogram"
    TEXTURE_FEATURES = "texture_features"
    SHAPE_FEATURES = "shape_features"
    COMBINED_FEATURES = "combined_features"


class ColorSpace(Enum):
    """颜色空间枚举"""
    RGB = "rgb"
    HSV = "hsv"
    LAB = "lab"
    LUV = "luv"


class TextureMethod(Enum):
    """纹理特征方法枚举"""
    LBP = "lbp"  # Local Binary Pattern
    GLCM = "glcm"  # Gray Level Co-occurrence Matrix
    GABOR = "gabor"  # Gabor Filter
    WAVELET = "wavelet"  # Wavelet Transform


@dataclass
class ColorHistogramConfig:
    """颜色直方图配置"""
    bins: int = 256
    color_space: ColorSpace = ColorSpace.RGB
    normalize: bool = True
    range_min: float = 0.0
    range_max: float = 255.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'bins': self.bins,
            'color_space': self.color_space.value,
            'normalize': self.normalize,
            'range_min': self.range_min,
            'range_max': self.range_max
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ColorHistogramConfig':
        """从字典创建"""
        return cls(
            bins=data.get('bins', 256),
            color_space=ColorSpace(data.get('color_space', 'rgb')),
            normalize=data.get('normalize', True),
            range_min=data.get('range_min', 0.0),
            range_max=data.get('range_max', 255.0)
        )


@dataclass
class TextureConfig:
    """纹理特征配置"""
    method: TextureMethod = TextureMethod.LBP
    radius: int = 3
    n_points: int = 24
    uniform: bool = True
    
    # GLCM 参数
    distances: List[int] = field(default_factory=lambda: [1, 2, 3])
    angles: List[float] = field(default_factory=lambda: [0, 45, 90, 135])
    
    # Gabor 参数
    frequencies: List[float] = field(default_factory=lambda: [0.1, 0.3, 0.5])
    orientations: List[float] = field(default_factory=lambda: [0, 45, 90, 135])
    
    # Wavelet 参数
    wavelet: str = 'db4'
    levels: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'method': self.method.value,
            'radius': self.radius,
            'n_points': self.n_points,
            'uniform': self.uniform,
            'distances': self.distances,
            'angles': self.angles,
            'frequencies': self.frequencies,
            'orientations': self.orientations,
            'wavelet': self.wavelet,
            'levels': self.levels
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TextureConfig':
        """从字典创建"""
        return cls(
            method=TextureMethod(data.get('method', 'lbp')),
            radius=data.get('radius', 3),
            n_points=data.get('n_points', 24),
            uniform=data.get('uniform', True),
            distances=data.get('distances', [1, 2, 3]),
            angles=data.get('angles', [0, 45, 90, 135]),
            frequencies=data.get('frequencies', [0.1, 0.3, 0.5]),
            orientations=data.get('orientations', [0, 45, 90, 135]),
            wavelet=data.get('wavelet', 'db4'),
            levels=data.get('levels', 3)
        )


@dataclass
class ShapeConfig:
    """形状特征配置"""
    contour_approximation: float = 0.02
    hu_moments: bool = True
    fourier_descriptors: bool = True
    fourier_descriptors_count: int = 20
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'contour_approximation': self.contour_approximation,
            'hu_moments': self.hu_moments,
            'fourier_descriptors': self.fourier_descriptors,
            'fourier_descriptors_count': self.fourier_descriptors_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShapeConfig':
        """从字典创建"""
        return cls(
            contour_approximation=data.get('contour_approximation', 0.02),
            hu_moments=data.get('hu_moments', True),
            fourier_descriptors=data.get('fourier_descriptors', True),
            fourier_descriptors_count=data.get('fourier_descriptors_count', 20)
        )


@dataclass
class DeepFeatureConfig:
    """深度特征配置"""
    model_name: str = 'resnet50'
    layer_name: Optional[str] = None
    input_size: tuple = (224, 224)
    normalize: bool = True
    use_gpu: bool = True
    batch_size: int = 32
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'model_name': self.model_name,
            'layer_name': self.layer_name,
            'input_size': list(self.input_size),
            'normalize': self.normalize,
            'use_gpu': self.use_gpu,
            'batch_size': self.batch_size
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeepFeatureConfig':
        """从字典创建"""
        input_size = data.get('input_size', [224, 224])
        if isinstance(input_size, list):
            input_size = tuple(input_size)
            
        return cls(
            model_name=data.get('model_name', 'resnet50'),
            layer_name=data.get('layer_name'),
            input_size=input_size,
            normalize=data.get('normalize', True),
            use_gpu=data.get('use_gpu', True),
            batch_size=data.get('batch_size', 32)
        )


@dataclass
class TraditionalFeatureConfig:
    """传统特征提取配置"""
    extract_color: bool = True
    extract_texture: bool = True
    extract_shape: bool = True
    hist_bins: int = 32
    n_dominant_colors: int = 5
    lbp_radius: int = 3
    lbp_n_points: int = 24
    n_fourier_descriptors: int = 32
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'extract_color': self.extract_color,
            'extract_texture': self.extract_texture,
            'extract_shape': self.extract_shape,
            'hist_bins': self.hist_bins,
            'n_dominant_colors': self.n_dominant_colors,
            'lbp_radius': self.lbp_radius,
            'lbp_n_points': self.lbp_n_points,
            'n_fourier_descriptors': self.n_fourier_descriptors
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TraditionalFeatureConfig':
        """从字典创建配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            TraditionalFeatureConfig: 配置实例
        """
        return cls(
            extract_color=config_dict.get('extract_color', True),
            extract_texture=config_dict.get('extract_texture', True),
            extract_shape=config_dict.get('extract_shape', True),
            hist_bins=config_dict.get('hist_bins', 32),
            n_dominant_colors=config_dict.get('n_dominant_colors', 5),
            lbp_radius=config_dict.get('lbp_radius', 3),
            lbp_n_points=config_dict.get('lbp_n_points', 24),
            n_fourier_descriptors=config_dict.get('n_fourier_descriptors', 32)
        )


@dataclass
class FeatureExtractorConfig:
    """特征提取器配置"""
    model_name: str = "resnet50"
    use_gpu: bool = True
    batch_size: int = 16
    feature_dim: int = 2048
    feature_dimension: int = 2048  # 兼容测试中使用的参数名
    normalize_features: bool = True
    normalize: bool = True  # 添加normalize参数，兼容测试
    use_cache: bool = True
    cache_dir: Optional[str] = None
    preprocessing_params: Dict[str, Any] = field(default_factory=dict)
    device: Optional[str] = None
    input_size: int = 224  # 输入图像尺寸
    
    @property
    def name(self) -> str:
        """获取模型名称（兼容性属性）"""
        return self.model_name
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            "model_name": self.model_name,
            "use_gpu": self.use_gpu,
            "batch_size": self.batch_size,
            "feature_dim": self.feature_dim,
            "use_cache": self.use_cache,
            "cache_dir": self.cache_dir,
            "preprocessing_params": self.preprocessing_params,
            "device": self.device,
            "input_size": self.input_size
        }


@dataclass
class FeatureConfig:
    """特征配置主类"""
    feature_type: FeatureType = FeatureType.DEEP_FEATURES
    
    # 各类特征配置
    color_histogram: ColorHistogramConfig = field(default_factory=ColorHistogramConfig)
    texture: TextureConfig = field(default_factory=TextureConfig)
    shape: ShapeConfig = field(default_factory=ShapeConfig)
    deep_feature: DeepFeatureConfig = field(default_factory=DeepFeatureConfig)
    traditional_feature: TraditionalFeatureConfig = field(default_factory=TraditionalFeatureConfig)
    
    # 组合特征权重
    feature_weights: Dict[str, float] = field(default_factory=lambda: {
        'color': 0.3,
        'texture': 0.3,
        'shape': 0.2,
        'deep': 0.2
    })
    
    # 预处理配置
    image_preprocessing: Dict[str, Any] = field(default_factory=lambda: {
        'resize': True,
        'target_size': (224, 224),
        'maintain_aspect_ratio': True,
        'normalize': True,
        'mean': [0.485, 0.456, 0.406],
        'std': [0.229, 0.224, 0.225]
    })
    
    # 缓存配置
    cache_features: bool = True
    cache_size_mb: int = 512
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'feature_type': self.feature_type.value,
            'color_histogram': self.color_histogram.to_dict(),
            'texture': self.texture.to_dict(),
            'shape': self.shape.to_dict(),
            'deep_feature': self.deep_feature.to_dict(),
            'traditional_feature': self.traditional_feature.to_dict(),
            'feature_weights': self.feature_weights,
            'image_preprocessing': self.image_preprocessing,
            'cache_features': self.cache_features,
            'cache_size_mb': self.cache_size_mb
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FeatureConfig':
        """从字典创建"""
        return cls(
            feature_type=FeatureType(data.get('feature_type', 'deep_features')),
            color_histogram=ColorHistogramConfig.from_dict(
                data.get('color_histogram', {})
            ),
            texture=TextureConfig.from_dict(
                data.get('texture', {})
            ),
            shape=ShapeConfig.from_dict(
                data.get('shape', {})
            ),
            deep_feature=DeepFeatureConfig.from_dict(
                data.get('deep_feature', {})
            ),
            traditional_feature=TraditionalFeatureConfig.from_dict(
                data.get('traditional_feature', {})
            ),
            feature_weights=data.get('feature_weights', {
                'color': 0.3,
                'texture': 0.3,
                'shape': 0.2,
                'deep': 0.2
            }),
            image_preprocessing=data.get('image_preprocessing', {
                'resize': True,
                'target_size': (224, 224),
                'maintain_aspect_ratio': True,
                'normalize': True,
                'mean': [0.485, 0.456, 0.406],
                'std': [0.229, 0.224, 0.225]
            }),
            cache_features=data.get('cache_features', True),
            cache_size_mb=data.get('cache_size_mb', 512)
        )
    
    def get_active_feature_config(self) -> Any:
        """获取当前激活的特征配置"""
        if self.feature_type == FeatureType.COLOR_HISTOGRAM:
            return self.color_histogram
        elif self.feature_type == FeatureType.TEXTURE_FEATURES:
            return self.texture
        elif self.feature_type == FeatureType.SHAPE_FEATURES:
            return self.shape
        elif self.feature_type == FeatureType.DEEP_FEATURES:
            return self.deep_feature
        else:
            # 组合特征返回所有配置
            return {
                'color_histogram': self.color_histogram,
                'texture': self.texture,
                'shape': self.shape,
                'deep_feature': self.deep_feature,
                'weights': self.feature_weights
            }
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查特征权重总和
            if self.feature_type == FeatureType.COMBINED_FEATURES:
                total_weight = sum(self.feature_weights.values())
                if abs(total_weight - 1.0) > 0.01:
                    return False
            
            # 检查图像预处理参数
            if 'target_size' in self.image_preprocessing:
                size = self.image_preprocessing['target_size']
                if not isinstance(size, (list, tuple)) or len(size) != 2:
                    return False
                if any(s <= 0 for s in size):
                    return False
            
            # 检查缓存大小
            if self.cache_size_mb <= 0:
                return False
                
            return True
            
        except Exception:
            return False