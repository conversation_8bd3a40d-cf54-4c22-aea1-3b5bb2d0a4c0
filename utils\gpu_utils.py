#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU工具模块

该模块提供GPU检测、管理和优化功能。
"""

import os
import logging
import platform
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import psutil

from .log_utils import LoggerMixin

# 尝试导入GPU相关库
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False
    cp = None

try:
    import pynvml
    PYNVML_AVAILABLE = True
except ImportError:
    PYNVML_AVAILABLE = False
    pynvml = None

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    tf = None


@dataclass
class GPUInfo:
    """GPU信息数据类"""
    device_id: int
    name: str
    total_memory: int  # 总内存（字节）
    available_memory: int  # 可用内存（字节）
    compute_capability: Optional[Tuple[int, int]] = None
    driver_version: Optional[str] = None
    cuda_version: Optional[str] = None
    is_available: bool = True
    utilization: float = 0.0      # 利用率(%)
    temperature: float = 0.0      # 温度(°C)
    power_usage: float = 0.0      # 功耗(W)


@dataclass
class GPUConfig:
    """GPU配置"""
    enable_gpu: bool = True
    preferred_device: str = 'auto'      # auto, cpu, cuda:0, cuda:1, etc.
    memory_fraction: float = 0.8        # GPU内存使用比例
    allow_growth: bool = True           # 是否允许内存增长
    mixed_precision: bool = False       # 是否使用混合精度
    batch_size_multiplier: float = 1.0  # 批处理大小倍数
    fallback_to_cpu: bool = True        # 是否回退到CPU


class GPUManager(LoggerMixin):
    """GPU管理器"""
    
    def __init__(self, config: Optional[GPUConfig] = None):
        """初始化GPU管理器
        
        Args:
            config: GPU配置
        """
        super().__init__()
        self.config = config or GPUConfig()
        self._gpu_info_cache = None
        self._device_cache = None
        self._initialized = False
        
        # 初始化GPU库
        self._init_gpu_libraries()
    
    def _init_gpu_libraries(self):
        """初始化GPU库"""
        try:
            # 初始化PYNVML
            if PYNVML_AVAILABLE:
                pynvml.nvmlInit()
                self.logger.debug("PYNVML初始化成功")
            
            # 设置PyTorch GPU配置
            if TORCH_AVAILABLE and torch.cuda.is_available():
                if self.config.memory_fraction < 1.0:
                    # 设置内存分配策略
                    torch.cuda.set_per_process_memory_fraction(self.config.memory_fraction)
                self.logger.debug("PyTorch CUDA配置完成")
            
            # 设置TensorFlow GPU配置
            if TF_AVAILABLE:
                try:
                    # 检查TensorFlow版本和GPU可用性
                    if hasattr(tf, 'config') and hasattr(tf.config, 'experimental'):
                        gpus = tf.config.experimental.list_physical_devices('GPU')
                        if gpus:
                            try:
                                for gpu in gpus:
                                    if self.config.allow_growth:
                                        tf.config.experimental.set_memory_growth(gpu, True)
                                    if self.config.memory_fraction < 1.0:
                                        tf.config.experimental.set_memory_limit(
                                            gpu, int(self._get_gpu_memory(0) * self.config.memory_fraction)
                                        )
                                self.logger.debug("TensorFlow GPU配置完成")
                            except RuntimeError as e:
                                self.logger.warning(f"TensorFlow GPU配置失败: {e}")
                    else:
                        self.logger.warning("TensorFlow版本不支持GPU配置API")
                except Exception as e:
                    self.logger.warning(f"TensorFlow GPU初始化失败: {e}")
            
            self._initialized = True
            
        except Exception as e:
            self.logger.error(f"GPU库初始化失败: {e}")
    
    def is_available(self) -> bool:
        """检查GPU是否可用
        
        Returns:
            bool: GPU是否可用
        """
        if not self.config.enable_gpu:
            return False
        
        torch_available = TORCH_AVAILABLE and torch.cuda.is_available()
        cupy_available = CUPY_AVAILABLE and cp.cuda.is_available()
        tf_available = False
        
        if TF_AVAILABLE:
            try:
                if hasattr(tf, 'config') and hasattr(tf.config, 'experimental'):
                    tf_available = len(tf.config.experimental.list_physical_devices('GPU')) > 0
            except Exception:
                tf_available = False
        
        return torch_available or cupy_available or tf_available
    
    def get_device_count(self) -> int:
        """获取GPU设备数量
        
        Returns:
            int: GPU设备数量
        """
        if not self.is_available():
            return 0
        
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                return torch.cuda.device_count()
            elif PYNVML_AVAILABLE:
                return pynvml.nvmlDeviceGetCount()
            elif TF_AVAILABLE:
                return len(tf.config.experimental.list_physical_devices('GPU'))
            else:
                return 0
        except Exception as e:
            self.logger.error(f"获取GPU设备数量失败: {e}")
            return 0
    
    def get_gpu_info(self, device_id: int = 0) -> Optional[GPUInfo]:
        """获取GPU信息
        
        Args:
            device_id: 设备ID
            
        Returns:
            GPUInfo: GPU信息
        """
        try:
            if PYNVML_AVAILABLE:
                return self._get_gpu_info_pynvml(device_id)
            elif TORCH_AVAILABLE and torch.cuda.is_available():
                return self._get_gpu_info_torch(device_id)
            else:
                return None
        except Exception as e:
            self.logger.error(f"获取GPU信息失败: {e}")
            return None
    
    def _get_gpu_info_pynvml(self, device_id: int) -> GPUInfo:
        """使用PYNVML获取GPU信息"""
        handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
        
        # 基本信息
        name_raw = pynvml.nvmlDeviceGetName(handle)
        name = name_raw.decode('utf-8') if isinstance(name_raw, bytes) else name_raw
        memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
        
        # 详细信息
        try:
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_util = utilization.gpu
        except (pynvml.NVMLError, AttributeError) as e:
            self.logger.debug(f"获取GPU利用率失败: {e}")
            gpu_util = 0.0
        
        try:
            temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
        except (pynvml.NVMLError, AttributeError) as e:
            self.logger.debug(f"获取GPU温度失败: {e}")
            temperature = 0.0
        
        try:
            power_usage = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为瓦特
        except (pynvml.NVMLError, AttributeError) as e:
            self.logger.debug(f"获取GPU功耗失败: {e}")
            power_usage = 0.0
        
        try:
            major, minor = pynvml.nvmlDeviceGetCudaComputeCapability(handle)
            compute_capability = (major, minor)
        except (pynvml.NVMLError, AttributeError) as e:
            self.logger.debug(f"获取GPU计算能力失败: {e}")
            compute_capability = None
        
        try:
            driver_version_raw = pynvml.nvmlSystemGetDriverVersion()
            driver_version = driver_version_raw.decode('utf-8') if isinstance(driver_version_raw, bytes) else driver_version_raw
        except (pynvml.NVMLError, AttributeError, UnicodeDecodeError) as e:
            self.logger.debug(f"获取驱动版本失败: {e}")
            driver_version = None
        
        try:
            cuda_version = pynvml.nvmlSystemGetCudaDriverVersion()
            cuda_version = f"{cuda_version // 1000}.{(cuda_version % 1000) // 10}"
        except (pynvml.NVMLError, AttributeError) as e:
            self.logger.debug(f"获取CUDA版本失败: {e}")
            cuda_version = None
        
        return GPUInfo(
            device_id=device_id,
            name=name,
            total_memory=memory_info.total,
            available_memory=memory_info.free,
            compute_capability=compute_capability,
            driver_version=driver_version,
            cuda_version=cuda_version,
            utilization=gpu_util,
            temperature=temperature,
            power_usage=power_usage
        )
    
    def _get_gpu_info_torch(self, device_id: int) -> GPUInfo:
        """使用PyTorch获取GPU信息"""
        if device_id >= torch.cuda.device_count():
            raise ValueError(f"设备ID {device_id} 超出范围")
        
        name = torch.cuda.get_device_name(device_id)
        total_memory = torch.cuda.get_device_properties(device_id).total_memory
        
        # 获取当前内存使用情况
        torch.cuda.empty_cache()
        allocated = torch.cuda.memory_allocated(device_id)
        available_memory = total_memory - allocated
        
        # 计算能力
        props = torch.cuda.get_device_properties(device_id)
        compute_capability = (props.major, props.minor)
        
        return GPUInfo(
            device_id=device_id,
            name=name,
            total_memory=total_memory,
            available_memory=available_memory,
            compute_capability=compute_capability
        )
    
    def _get_gpu_memory(self, device_id: int) -> int:
        """获取GPU总内存"""
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                return torch.cuda.get_device_properties(device_id).total_memory
            elif PYNVML_AVAILABLE:
                handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                return memory_info.total
            else:
                return 0
        except (RuntimeError, pynvml.NVMLError, IndexError) as e:
            self.logger.debug(f"获取GPU内存失败: {e}")
            return 0
    
    def get_all_gpu_info(self) -> List[GPUInfo]:
        """获取所有GPU信息
        
        Returns:
            List[GPUInfo]: 所有GPU信息列表
        """
        gpu_infos = []
        device_count = self.get_device_count()
        
        for device_id in range(device_count):
            gpu_info = self.get_gpu_info(device_id)
            if gpu_info:
                gpu_infos.append(gpu_info)
        
        return gpu_infos
    
    def get_optimal_device(self, min_memory_gb: float = 1.0) -> str:
        """获取最优设备
        
        Args:
            min_memory_gb: 最小内存要求（GB）
            
        Returns:
            str: 设备名称（如'cuda:0', 'cpu'）
        """
        if self.config.preferred_device != 'auto':
            return self.config.preferred_device
        
        if not self.is_available():
            return 'cpu'
        
        # 获取最佳GPU设备
        best_device_id = self.get_best_device_id(min_memory_gb)
        if best_device_id is not None:
            return f'cuda:{best_device_id}'
        
        if self.config.fallback_to_cpu:
            self.logger.info("回退到CPU")
            return 'cpu'
        
        return 'cpu'
    
    def get_best_device_id(self, min_memory_gb: float = 1.0) -> Optional[int]:
        """获取最佳GPU设备ID
        
        Args:
            min_memory_gb: 最小内存要求（GB）
            
        Returns:
            Optional[int]: 最佳GPU设备ID
        """
        if not self.is_available():
            return None
        
        gpu_infos = self.get_all_gpu_info()
        if not gpu_infos:
            return None
        
        min_memory_bytes = min_memory_gb * 1024**3
        
        # 筛选满足内存要求的GPU
        suitable_gpus = [
            gpu for gpu in gpu_infos 
            if gpu.available_memory >= min_memory_bytes
        ]
        
        if not suitable_gpus:
            self.logger.warning(f"没有GPU满足最小内存要求 {min_memory_gb}GB")
            return None
        
        # 选择利用率最低且可用内存最多的GPU
        best_gpu = min(suitable_gpus, key=lambda x: (x.utilization, -x.available_memory))
        
        self.logger.info(
            f"选择GPU {best_gpu.device_id}: {best_gpu.name}, "
            f"可用内存: {best_gpu.available_memory / 1024**3:.1f}GB, "
            f"利用率: {best_gpu.utilization:.1f}%"
        )
        
        return best_gpu.device_id
    
    def set_device(self, device: str) -> bool:
        """设置当前设备
        
        Args:
            device: 设备名称（如'cuda:0', 'cpu'）
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if device == 'cpu':
                self.logger.info("使用CPU设备")
                return True
            
            if device.startswith('cuda:'):
                device_id = int(device.split(':')[1])
                
                if TORCH_AVAILABLE and torch.cuda.is_available():
                    if device_id >= torch.cuda.device_count():
                        self.logger.error(f"GPU设备ID {device_id} 超出范围")
                        return False
                    torch.cuda.set_device(device_id)
                    self.logger.info(f"当前GPU设备设置为: {device_id}")
                    return True
            
            self.logger.error(f"不支持的设备: {device}")
            return False
            
        except Exception as e:
            self.logger.error(f"设置设备失败: {e}")
            return False
    
    def clear_cache(self, device_id: Optional[int] = None):
        """清理GPU缓存
        
        Args:
            device_id: GPU设备ID，如果为None则清理当前设备
        """
        if not self.is_available():
            return
        
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                if device_id is not None:
                    current_device = torch.cuda.current_device()
                    torch.cuda.set_device(device_id)
                    torch.cuda.empty_cache()
                    torch.cuda.set_device(current_device)
                    self.logger.info(f"GPU {device_id} 缓存已清理")
                else:
                    torch.cuda.empty_cache()
                    self.logger.info("当前GPU缓存已清理")
            
            if CUPY_AVAILABLE:
                cp.get_default_memory_pool().free_all_blocks()
                self.logger.info("CuPy内存池已清理")
                
        except Exception as e:
            self.logger.error(f"清理GPU缓存失败: {e}")
    
    def get_memory_info(self, device_id: int = 0) -> Dict[str, int]:
        """获取GPU内存信息
        
        Args:
            device_id: GPU设备ID
            
        Returns:
            Dict[str, int]: 内存信息字典
        """
        if not self.is_available():
            return {}
        
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated(device_id)
                reserved = torch.cuda.memory_reserved(device_id)
                
                # 获取总内存
                props = torch.cuda.get_device_properties(device_id)
                total = props.total_memory
                
                return {
                    'total': total,
                    'allocated': allocated,
                    'reserved': reserved,
                    'free': total - reserved
                }
            
            elif PYNVML_AVAILABLE:
                handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                return {
                    'total': memory_info.total,
                    'allocated': memory_info.used,
                    'reserved': memory_info.used,
                    'free': memory_info.free
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"获取GPU内存信息失败: {e}")
            return {}
    
    def monitor_gpu_usage(self, device_id: int = 0) -> Dict[str, float]:
        """监控GPU使用情况
        
        Args:
            device_id: GPU设备ID
            
        Returns:
            Dict[str, float]: 使用情况字典
        """
        if not self.is_available():
            return {}
        
        try:
            if PYNVML_AVAILABLE:
                handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                
                # 获取利用率
                try:
                    utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    gpu_util = utilization.gpu
                    memory_util = utilization.memory
                except (pynvml.NVMLError, AttributeError) as e:
                    self.logger.debug(f"获取GPU利用率失败: {e}")
                    gpu_util = memory_util = 0.0
                
                # 获取温度
                try:
                    temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                except (pynvml.NVMLError, AttributeError) as e:
                    self.logger.debug(f"获取GPU温度失败: {e}")
                    temperature = 0.0
                
                # 获取功耗
                try:
                    power_usage = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0
                except (pynvml.NVMLError, AttributeError) as e:
                    self.logger.debug(f"获取GPU功耗失败: {e}")
                    power_usage = 0.0
                
                return {
                    'gpu_utilization': gpu_util,
                    'memory_utilization': memory_util,
                    'temperature': temperature,
                    'power_usage': power_usage
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"监控GPU使用情况失败: {e}")
            return {}
    
    def recommend_batch_size(self, model_memory_mb: float, device_id: int = 0) -> int:
        """推荐批处理大小
        
        Args:
            model_memory_mb: 模型内存占用（MB）
            device_id: GPU设备ID
            
        Returns:
            int: 推荐的批处理大小
        """
        if not self.is_available():
            return 1
        
        try:
            memory_info = self.get_memory_info(device_id)
            if not memory_info:
                return 1
            
            # 可用内存（MB）
            available_mb = memory_info['free'] / (1024 * 1024)
            
            # 保留一些内存用于其他操作
            usable_mb = available_mb * 0.8
            
            # 计算批处理大小
            batch_size = max(1, int(usable_mb / model_memory_mb))
            
            # 应用配置的倍数
            batch_size = int(batch_size * self.config.batch_size_multiplier)
            
            self.logger.info(
                f"推荐批处理大小: {batch_size} "
                f"(可用内存: {available_mb:.1f}MB, 模型内存: {model_memory_mb:.1f}MB)"
            )
            
            return max(1, batch_size)
            
        except Exception as e:
            self.logger.error(f"推荐批处理大小失败: {e}")
            return 1
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息
        
        Returns:
            Dict[str, Any]: 系统信息
        """
        info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total,
            'memory_available': psutil.virtual_memory().available,
            'gpu_available': self.is_available(),
            'gpu_count': self.get_device_count(),
            'libraries': {
                'torch': TORCH_AVAILABLE,
                'cupy': CUPY_AVAILABLE,
                'tensorflow': TF_AVAILABLE,
                'pynvml': PYNVML_AVAILABLE
            }
        }
        
        if self.is_available():
            info['gpu_info'] = [gpu.__dict__ for gpu in self.get_all_gpu_info()]
        
        return info
    
    def optimize_memory_usage(self, device_id: Optional[int] = None):
        """优化GPU内存使用
        
        Args:
            device_id: GPU设备ID，如果为None则优化当前设备
        """
        if not self.is_available():
            return
        
        try:
            # 清理缓存
            self.clear_cache(device_id)
            
            # 设置内存分配策略
            if TORCH_AVAILABLE and torch.cuda.is_available():
                # 启用内存映射
                torch.backends.cudnn.benchmark = True
                
                # 设置内存分配器
                if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                    torch.cuda.set_per_process_memory_fraction(self.config.memory_fraction)
                
                self.logger.info("PyTorch GPU内存优化完成")
            
            # CuPy内存优化
            if CUPY_AVAILABLE:
                # 设置内存池大小限制
                mempool = cp.get_default_memory_pool()
                mempool.set_limit(size=None)  # 移除限制
                
                self.logger.info("CuPy内存优化完成")
            
        except Exception as e:
            self.logger.error(f"GPU内存优化失败: {e}")
    
    def enable_mixed_precision(self) -> bool:
        """启用混合精度训练
        
        Returns:
            bool: 是否成功启用
        """
        if not self.config.mixed_precision:
            return False
        
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                # 检查是否支持混合精度
                if torch.cuda.get_device_capability()[0] >= 7:  # Volta架构及以上
                    torch.backends.cudnn.allow_tf32 = True
                    torch.backends.cuda.matmul.allow_tf32 = True
                    self.logger.info("混合精度训练已启用")
                    return True
                else:
                    self.logger.warning("当前GPU不支持混合精度训练")
                    return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"启用混合精度失败: {e}")
            return False
    
    def get_compute_capability(self, device_id: int = 0) -> Optional[Tuple[int, int]]:
        """获取GPU计算能力
        
        Args:
            device_id: GPU设备ID
            
        Returns:
            Optional[Tuple[int, int]]: 计算能力版本
        """
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                capability = torch.cuda.get_device_capability(device_id)
                return capability
            elif PYNVML_AVAILABLE:
                handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                major, minor = pynvml.nvmlDeviceGetCudaComputeCapability(handle)
                return (major, minor)
            else:
                return None
        except Exception as e:
            self.logger.error(f"获取计算能力失败: {e}")
            return None
    
    def is_tensor_core_available(self, device_id: int = 0) -> bool:
        """检查是否支持Tensor Core
        
        Args:
            device_id: GPU设备ID
            
        Returns:
            bool: 是否支持Tensor Core
        """
        capability = self.get_compute_capability(device_id)
        if capability:
            major, minor = capability
            # Volta (7.0) 及以上架构支持Tensor Core
            return major >= 7
        return False


# 全局GPU管理器实例
_gpu_manager: Optional[GPUManager] = None


def get_gpu_manager(config: Optional[GPUConfig] = None) -> GPUManager:
    """获取全局GPU管理器实例
    
    Args:
        config: GPU配置
        
    Returns:
        GPUManager: GPU管理器实例
    """
    global _gpu_manager
    if _gpu_manager is None:
        _gpu_manager = GPUManager(config)
    return _gpu_manager


def is_gpu_available() -> bool:
    """检查GPU是否可用
    
    Returns:
        bool: GPU是否可用
    """
    return get_gpu_manager().is_available()


def get_optimal_device(min_memory_gb: float = 1.0) -> str:
    """获取最优设备
    
    Args:
        min_memory_gb: 最小内存要求（GB）
        
    Returns:
        str: 设备名称
    """
    return get_gpu_manager().get_optimal_device(min_memory_gb)


def clear_gpu_cache(device_id: Optional[int] = None):
    """清理GPU缓存
    
    Args:
        device_id: GPU设备ID
    """
    get_gpu_manager().clear_cache(device_id)


def get_gpu_memory_info(device_id: int = 0) -> Dict[str, int]:
    """获取GPU内存信息
    
    Args:
        device_id: GPU设备ID
        
    Returns:
        Dict[str, int]: 内存信息
    """
    return get_gpu_manager().get_memory_info(device_id)


def recommend_batch_size(model_memory_mb: float, device_id: int = 0) -> int:
    """推荐批处理大小
    
    Args:
        model_memory_mb: 模型内存占用（MB）
        device_id: GPU设备ID
        
    Returns:
        int: 推荐的批处理大小
    """
    return get_gpu_manager().recommend_batch_size(model_memory_mb, device_id)


def optimize_gpu_memory(device_id: Optional[int] = None):
    """优化GPU内存使用
    
    Args:
        device_id: GPU设备ID
    """
    get_gpu_manager().optimize_memory_usage(device_id)


def get_system_info() -> Dict[str, Any]:
    """获取系统信息
    
    Returns:
        Dict[str, Any]: 系统信息
    """
    return get_gpu_manager().get_system_info()


# 兼容性函数
def setup_gpu_environment(config: Optional[GPUConfig] = None) -> GPUManager:
    """设置GPU环境
    
    Args:
        config: GPU配置
        
    Returns:
        GPUManager: GPU管理器实例
    """
    manager = get_gpu_manager(config)
    manager.optimize_memory_usage()
    manager.enable_mixed_precision()
    return manager