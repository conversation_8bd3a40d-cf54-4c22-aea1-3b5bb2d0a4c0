from PIL import Image
import os

def create_test_images():
    # 确保目录存在
    os.makedirs('test_images', exist_ok=True)
    
    # 创建测试图片
    for i in range(1, 5):
        # 创建一个300x300的红色图片
        img = Image.new('RGB', (300, 300), color=(255, 0, 0))
        # 保存图片
        img.save(f'test_images/fabric_{i}.jpg')
        print(f'Created test_images/fabric_{i}.jpg')

if __name__ == '__main__':
    create_test_images()