"""
搜索引擎单元测试
"""

import numpy as np
from unittest.mock import Mock, patch
import pytest

from search.search_engine import SearchEngine
from search.similarity_search import SimilaritySearch
from search.search_request import SearchRequest
from search.search_response import SearchResponse


class TestSearchEngine:
    """搜索引擎测试类"""

    def test_init(self):
        """测试初始化"""
        engine = SearchEngine()
        assert engine is not None
        assert hasattr(engine, 'index')

    def test_build_index(self, sample_images):
        """测试构建索引"""
        engine = SearchEngine()
        
        # 模拟特征数据
        features_data = {
            img['path']: img['features'] for img in sample_images
        }
        
        with patch.object(engine, 'index') as mock_index:
            mock_index.build_index.return_value = True
            
            result = engine.build_index(features_data)
            assert result is True
            mock_index.build_index.assert_called_once()

    def test_search(self, sample_images):
        """测试搜索功能"""
        engine = SearchEngine()
        
        # 模拟查询特征
        query_features = {
            'color': [0.1, 0.2, 0.3, 0.4, 0.5],
            'texture': [0.6, 0.7, 0.8, 0.9, 1.0],
            'shape': [1.1, 1.2, 1.3, 1.4, 1.5]
        }
        
        # 模拟搜索结果
        mock_results = [
            {'image_path': '/path/to/image1.jpg', 'similarity': 0.95},
            {'image_path': '/path/to/image2.jpg', 'similarity': 0.85}
        ]
        
        with patch.object(engine, 'index') as mock_index:
            mock_index.search.return_value = mock_results
            
            results = engine.search(query_features, top_k=5)
            
            assert len(results) == 2
            assert results[0]['similarity'] == 0.95
            mock_index.search.assert_called_once()

    def test_search_with_filters(self):
        """测试带过滤器的搜索"""
        engine = SearchEngine()
        
        query_features = {'color': [0.1, 0.2, 0.3]}
        
        with patch.object(engine, 'index') as mock_index:
            mock_index.search.return_value = []
            
            # 测试类别过滤器
            filters = {'category': 'fabric'}
            engine.search(query_features, filters=filters)
            
            # 验证过滤器被传递
            call_args = mock_index.search.call_args
            assert 'filters' in call_args[1]
            assert call_args[1]['filters']['category'] == 'fabric'

    def test_batch_search(self):
        """测试批量搜索"""
        engine = SearchEngine()
        
        queries = [
            {'color': [0.1, 0.2, 0.3]},
            {'color': [0.4, 0.5, 0.6]}
        ]
        
        with patch.object(engine, 'search') as mock_search:
            mock_search.return_value = [{'similarity': 0.8}]
            
            results = engine.batch_search(queries)
            
            assert len(results) == 2
            assert mock_search.call_count == 2

    def test_get_search_statistics(self):
        """测试获取搜索统计"""
        engine = SearchEngine()
        
        with patch.object(engine, 'index') as mock_index:
            mock_index.get_statistics.return_value = {
                'total_images': 1000,
                'index_size': '50MB',
                'last_updated': '2024-01-01'
            }
            
            stats = engine.get_search_statistics()
            
            assert 'total_images' in stats
            assert stats['total_images'] == 1000

    def test_update_index(self):
        """测试更新索引"""
        engine = SearchEngine()
        
        new_features = {
            '/path/to/new_image.jpg': {
                'color': [0.1, 0.2, 0.3],
                'texture': [0.4, 0.5, 0.6]
            }
        }
        
        with patch.object(engine, 'index') as mock_index:
            mock_index.update_index.return_value = True
            
            result = engine.update_index(new_features)
            assert result is True
            mock_index.update_index.assert_called_once_with(new_features)


class TestSimilaritySearch:
    """相似性搜索测试类"""

    def test_init(self):
        """测试初始化"""
        search = SimilaritySearch()
        assert search is not None

    def test_cosine_similarity(self):
        """测试余弦相似度计算"""
        search = SimilaritySearch()
        
        vec1 = np.array([1, 2, 3])
        vec2 = np.array([4, 5, 6])
        
        similarity = search.cosine_similarity(vec1, vec2)
        assert 0 <= similarity <= 1

    def test_euclidean_distance(self):
        """测试欧几里得距离计算"""
        search = SimilaritySearch()
        
        vec1 = np.array([1, 2, 3])
        vec2 = np.array([4, 5, 6])
        
        distance = search.euclidean_distance(vec1, vec2)
        assert distance >= 0

    def test_manhattan_distance(self):
        """测试曼哈顿距离计算"""
        search = SimilaritySearch()
        
        vec1 = np.array([1, 2, 3])
        vec2 = np.array([4, 5, 6])
        
        distance = search.manhattan_distance(vec1, vec2)
        assert distance >= 0

    def test_weighted_similarity(self):
        """测试加权相似度计算"""
        search = SimilaritySearch()
        
        features1 = {
            'color': [0.1, 0.2, 0.3],
            'texture': [0.4, 0.5, 0.6],
            'shape': [0.7, 0.8, 0.9]
        }
        
        features2 = {
            'color': [0.2, 0.3, 0.4],
            'texture': [0.5, 0.6, 0.7],
            'shape': [0.8, 0.9, 1.0]
        }
        
        weights = {'color': 0.4, 'texture': 0.3, 'shape': 0.3}
        
        similarity = search.weighted_similarity(features1, features2, weights)
        assert 0 <= similarity <= 1

    def test_find_k_nearest(self):
        """测试K近邻搜索"""
        search = SimilaritySearch()
        
        query = np.array([0.5, 0.5, 0.5])
        candidates = [
            np.array([0.1, 0.1, 0.1]),
            np.array([0.9, 0.9, 0.9]),
            np.array([0.6, 0.6, 0.6])
        ]
        
        nearest = search.find_k_nearest(query, candidates, k=2)
        assert len(nearest) == 2
        assert nearest[0][1] >= nearest[1][1]  # 相似度递减


class TestSearchRequest:
    """搜索请求测试类"""

    def test_init(self):
        """测试初始化"""
        request = SearchRequest(
            query_image='/path/to/query.jpg',
            top_k=10,
            similarity_threshold=0.5
        )
        
        assert request.query_image == '/path/to/query.jpg'
        assert request.top_k == 10
        assert request.similarity_threshold == 0.5

    def test_validate(self):
        """测试请求验证"""
        # 有效请求
        request = SearchRequest(
            query_image='/path/to/query.jpg',
            top_k=10
        )
        assert request.validate() is True
        
        # 无效请求 - 缺少查询图像
        request = SearchRequest(query_image='')
        assert request.validate() is False
        
        # 无效请求 - top_k为负数
        request = SearchRequest(
            query_image='/path/to/query.jpg',
            top_k=-1
        )
        assert request.validate() is False

    def test_to_dict(self):
        """测试转换为字典"""
        request = SearchRequest(
            query_image='/path/to/query.jpg',
            top_k=10,
            filters={'category': 'fabric'}
        )
        
        data = request.to_dict()
        assert data['query_image'] == '/path/to/query.jpg'
        assert data['top_k'] == 10
        assert data['filters']['category'] == 'fabric'


class TestSearchResponse:
    """搜索响应测试类"""

    def test_init(self):
        """测试初始化"""
        results = [
            {'image_path': '/path/to/image1.jpg', 'similarity': 0.95},
            {'image_path': '/path/to/image2.jpg', 'similarity': 0.85}
        ]
        
        response = SearchResponse(
            results=results,
            total_count=2,
            search_time=0.5
        )
        
        assert len(response.results) == 2
        assert response.total_count == 2
        assert response.search_time == 0.5

    def test_sort_results(self):
        """测试结果排序"""
        results = [
            {'image_path': '/path/to/image2.jpg', 'similarity': 0.85},
            {'image_path': '/path/to/image1.jpg', 'similarity': 0.95}
        ]
        
        response = SearchResponse(results=results)
        response.sort_results()
        
        assert response.results[0]['similarity'] == 0.95
        assert response.results[1]['similarity'] == 0.85

    def test_filter_results(self):
        """测试结果过滤"""
        results = [
            {'image_path': '/path/to/image1.jpg', 'similarity': 0.95},
            {'image_path': '/path/to/image2.jpg', 'similarity': 0.85},
            {'image_path': '/path/to/image3.jpg', 'similarity': 0.75}
        ]
        
        response = SearchResponse(results=results)
        filtered = response.filter_results(similarity_threshold=0.8)
        
        assert len(filtered) == 2
        assert all(r['similarity'] >= 0.8 for r in filtered)

    def test_to_dict(self):
        """测试转换为字典"""
        results = [
            {'image_path': '/path/to/image1.jpg', 'similarity': 0.95}
        ]
        
        response = SearchResponse(
            results=results,
            total_count=1,
            search_time=0.5
        )
        
        data = response.to_dict()
        assert 'results' in data
        assert 'total_count' in data
        assert 'search_time' in data
        assert len(data['results']) == 1 