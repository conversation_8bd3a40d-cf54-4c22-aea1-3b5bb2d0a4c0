"""
搜索管道集成测试
"""

import time
from pathlib import Path
from unittest.mock import patch

from features.core.feature_extractor import FeatureExtractor
from features.extractors.color_extractor import ColorExtractor
from features.extractors.texture_extractor import TextureExtractor
from features.extractors.shape_extractor import ShapeExtractor
from search.search_engine import SearchEngine
from search.similarity_search import SimilaritySearch
from features.index.feature_index import FeatureIndex


class TestSearchPipeline:
    """搜索管道集成测试类"""

    def test_complete_search_pipeline(self, test_images_dir, temp_dir):
        """测试完整的搜索管道"""
        # 初始化组件
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        similarity_search = SimilaritySearch()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        # 获取测试图像
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        assert len(image_paths) > 0
        
        # 提取特征并构建索引
        features_data = {}
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建搜索索引
        search_engine.build_index(features_data)
        
        # 执行搜索
        query_image_path = str(image_paths[0])
        query_features = features_data[query_image_path]
        
        results = search_engine.search(query_features, top_k=5)
        
        # 验证搜索结果
        assert len(results) > 0
        assert results[0]['similarity'] >= 0.8  # 自相似度应该很高
        
        # 验证结果排序
        for i in range(1, len(results)):
            assert results[i-1]['similarity'] >= results[i]['similarity']

    def test_search_with_filters(self, test_images_dir, temp_dir):
        """测试带过滤器的搜索"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for i, image_path in enumerate(image_paths):
            features = extractor.extract_features(str(image_path))
            # 添加元数据
            features['metadata'] = {
                'category': 'fabric' if i % 2 == 0 else 'textile',
                'size': 'large' if i < len(image_paths) // 2 else 'small'
            }
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 测试类别过滤器
        query_features = features_data[str(image_paths[0])]
        filters = {'category': 'fabric'}
        
        results = search_engine.search(query_features, filters=filters, top_k=10)
        
        # 验证过滤结果
        assert len(results) > 0
        # 所有结果应该都是fabric类别
        for result in results:
            image_path = result['image_path']
            assert features_data[image_path]['metadata']['category'] == 'fabric'

    def test_batch_search_pipeline(self, test_images_dir, temp_dir):
        """测试批量搜索管道"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 准备批量查询
        query_features_list = [
            features_data[str(image_paths[0])],
            features_data[str(image_paths[1])]
        ]
        
        # 执行批量搜索
        batch_results = search_engine.batch_search(query_features_list, top_k=3)
        
        # 验证批量搜索结果
        assert len(batch_results) == 2
        for results in batch_results:
            assert len(results) > 0
            assert all('similarity' in result for result in results)

    def test_search_performance(self, test_images_dir, temp_dir):
        """测试搜索性能"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 性能测试
        query_features = features_data[str(image_paths[0])]
        
        # 测量搜索时间
        start_time = time.time()
        for _ in range(10):  # 执行10次搜索
            results = search_engine.search(query_features, top_k=5)
        end_time = time.time()
        
        avg_search_time = (end_time - start_time) / 10
        
        # 验证性能指标
        assert avg_search_time < 0.1  # 平均搜索时间应该小于0.1秒
        assert len(results) > 0

    def test_search_accuracy(self, test_images_dir, temp_dir):
        """测试搜索准确性"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 测试自相似度
        for image_path in image_paths[:3]:  # 测试前3个图像
            query_features = features_data[str(image_path)]
            results = search_engine.search(query_features, top_k=1)
            
            # 第一个结果应该是查询图像本身
            assert results[0]['image_path'] == str(image_path)
            assert results[0]['similarity'] >= 0.99  # 自相似度应该接近1

    def test_search_with_different_similarity_metrics(self, test_images_dir, temp_dir):
        """测试不同相似度度量的搜索"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        similarity_search = SimilaritySearch()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        query_features = features_data[str(image_paths[0])]
        
        # 测试不同相似度度量
        similarity_metrics = ['cosine', 'euclidean', 'manhattan']
        
        for metric in similarity_metrics:
            results = search_engine.search(
                query_features, 
                top_k=3, 
                similarity_metric=metric
            )
            
            assert len(results) > 0
            assert all('similarity' in result for result in results)

    def test_search_error_handling(self, temp_dir):
        """测试搜索错误处理"""
        search_engine = SearchEngine()
        
        # 测试空索引搜索
        with patch.object(search_engine, 'index') as mock_index:
            mock_index.get_total_count.return_value = 0
            
            # 应该优雅地处理空索引
            results = search_engine.search({'color': [0.1, 0.2, 0.3]})
            assert len(results) == 0
        
        # 测试无效查询特征
        with patch.object(search_engine, 'index') as mock_index:
            mock_index.search.side_effect = ValueError("Invalid features")
            
            # 应该优雅地处理错误
            try:
                results = search_engine.search(None)
                assert results == []
            except Exception:
                # 或者抛出异常但被正确处理
                pass

    def test_search_with_weighted_features(self, test_images_dir, temp_dir):
        """测试加权特征搜索"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        query_features = features_data[str(image_paths[0])]
        
        # 测试不同权重配置
        weight_configs = [
            {'color': 0.7, 'texture': 0.3},
            {'color': 0.3, 'texture': 0.7},
            {'color': 0.5, 'texture': 0.5}
        ]
        
        for weights in weight_configs:
            results = search_engine.search(
                query_features, 
                top_k=3, 
                feature_weights=weights
            )
            
            assert len(results) > 0
            assert all('similarity' in result for result in results)

    def test_search_statistics(self, test_images_dir, temp_dir):
        """测试搜索统计功能"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 获取搜索统计
        stats = search_engine.get_search_statistics()
        
        # 验证统计信息
        assert 'total_images' in stats
        assert stats['total_images'] == len(image_paths)
        assert 'index_size' in stats
        assert 'last_updated' in stats 