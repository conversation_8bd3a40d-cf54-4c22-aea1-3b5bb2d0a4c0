"""
系统性能测试
"""

import time
import psutil
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import pytest

from features.core.feature_extractor import FeatureExtractor
from features.extractors.color_extractor import ColorExtractor
from features.extractors.texture_extractor import TextureExtractor
from features.extractors.shape_extractor import ShapeExtractor
from search.search_engine import SearchEngine
from features.storage.feature_storage import FeatureStorage


class TestSystemPerformance:
    """系统性能测试类"""

    def test_feature_extraction_performance(self, test_images_dir, temp_dir):
        """测试特征提取性能"""
        extractor = FeatureExtractor()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        # 获取测试图像
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 测量特征提取时间
        start_time = time.time()
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        extraction_time = time.time() - start_time
        
        # 计算性能指标
        avg_time_per_image = extraction_time / len(image_paths)
        images_per_second = len(image_paths) / extraction_time
        
        # 验证性能指标
        assert avg_time_per_image < 2.0  # 每个图像处理时间小于2秒
        assert images_per_second > 0.5  # 每秒处理至少0.5个图像
        
        print(f"特征提取性能: {images_per_second:.2f} 图像/秒")

    def test_search_performance(self, test_images_dir, temp_dir):
        """测试搜索性能"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 性能测试
        query_features = features_data[str(image_paths[0])]
        
        # 测量搜索时间
        search_times = []
        for _ in range(20):  # 执行20次搜索
            start_time = time.time()
            results = search_engine.search(query_features, top_k=10)
            end_time = time.time()
            search_times.append(end_time - start_time)
        
        # 计算统计指标
        avg_search_time = sum(search_times) / len(search_times)
        min_search_time = min(search_times)
        max_search_time = max(search_times)
        
        # 验证性能指标
        assert avg_search_time < 0.1  # 平均搜索时间小于0.1秒
        assert max_search_time < 0.5  # 最大搜索时间小于0.5秒
        
        print(f"搜索性能: 平均 {avg_search_time*1000:.2f}ms, "
              f"最小 {min_search_time*1000:.2f}ms, "
              f"最大 {max_search_time*1000:.2f}ms")

    def test_memory_usage(self, test_images_dir, temp_dir):
        """测试内存使用情况"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        # 处理图像
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
            storage.save_features(str(image_path), features)
        
        # 构建索引
        search_engine.build_index(features_data)
        
        # 测量内存使用
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 验证内存使用
        assert memory_increase < 500 * 1024 * 1024  # 内存增长小于500MB
        
        print(f"内存使用: 增长 {memory_increase / 1024 / 1024:.2f}MB")

    def test_concurrent_feature_extraction(self, test_images_dir, temp_dir):
        """测试并发特征提取"""
        extractor = FeatureExtractor()
        extractor.register_extractor(ColorExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        def extract_features(image_path):
            return extractor.extract_features(str(image_path))
        
        # 测试不同并发数
        concurrency_levels = [1, 2, 4]
        
        for num_workers in concurrency_levels:
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(extract_features, path) 
                          for path in image_paths]
                results = [future.result() for future in as_completed(futures)]
            
            total_time = time.time() - start_time
            throughput = len(image_paths) / total_time
            
            print(f"并发数 {num_workers}: {throughput:.2f} 图像/秒")
            
            # 验证所有特征都被提取
            assert len(results) == len(image_paths)
            assert all(result is not None for result in results)

    def test_concurrent_search(self, test_images_dir, temp_dir):
        """测试并发搜索"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        extractor.register_extractor(ColorExtractor())
        
        # 准备测试数据
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        features_data = {}
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        search_engine.build_index(features_data)
        
        # 准备查询
        query_features = features_data[str(image_paths[0])]
        
        def perform_search():
            return search_engine.search(query_features, top_k=5)
        
        # 测试并发搜索
        num_concurrent_searches = 10
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_concurrent_searches) as executor:
            futures = [executor.submit(perform_search) 
                      for _ in range(num_concurrent_searches)]
            results = [future.result() for future in as_completed(futures)]
        
        total_time = time.time() - start_time
        avg_time_per_search = total_time / num_concurrent_searches
        
        # 验证结果
        assert len(results) == num_concurrent_searches
        assert all(len(result) > 0 for result in results)
        assert avg_time_per_search < 0.1  # 平均搜索时间小于0.1秒
        
        print(f"并发搜索性能: 平均 {avg_time_per_search*1000:.2f}ms/搜索")

    def test_storage_performance(self, test_images_dir, temp_dir):
        """测试存储性能"""
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        extractor.register_extractor(ColorExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 测量存储性能
        storage_times = []
        load_times = []
        
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            
            # 测量存储时间
            start_time = time.time()
            storage.save_features(str(image_path), features)
            storage_time = time.time() - start_time
            storage_times.append(storage_time)
            
            # 测量加载时间
            start_time = time.time()
            loaded_features = storage.load_features(str(image_path))
            load_time = time.time() - start_time
            load_times.append(load_time)
            
            # 验证数据一致性
            assert loaded_features is not None
            assert 'color' in loaded_features
        
        # 计算性能指标
        avg_storage_time = sum(storage_times) / len(storage_times)
        avg_load_time = sum(load_times) / len(load_times)
        
        # 验证性能
        assert avg_storage_time < 0.1  # 平均存储时间小于0.1秒
        assert avg_load_time < 0.05  # 平均加载时间小于0.05秒
        
        print(f"存储性能: 存储 {avg_storage_time*1000:.2f}ms, "
              f"加载 {avg_load_time*1000:.2f}ms")

    def test_system_throughput(self, test_images_dir, temp_dir):
        """测试系统整体吞吐量"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册所有提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 端到端性能测试
        start_time = time.time()
        
        # 1. 特征提取
        features_data = {}
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
        
        # 2. 存储特征
        for image_path, features in features_data.items():
            storage.save_features(image_path, features)
        
        # 3. 构建索引
        search_engine.build_index(features_data)
        
        # 4. 执行搜索
        query_features = features_data[str(image_paths[0])]
        results = search_engine.search(query_features, top_k=5)
        
        total_time = time.time() - start_time
        
        # 计算吞吐量
        total_operations = len(image_paths) * 3 + 1  # 提取+存储+索引+搜索
        operations_per_second = total_operations / total_time
        
        # 验证性能
        assert total_time < 30  # 总时间小于30秒
        assert operations_per_second > 0.1  # 每秒操作数大于0.1
        
        print(f"系统吞吐量: {operations_per_second:.2f} 操作/秒")

    def test_cpu_usage(self, test_images_dir, temp_dir):
        """测试CPU使用情况"""
        process = psutil.Process(os.getpid())
        
        extractor = FeatureExtractor()
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 测量CPU使用
        cpu_percentages = []
        
        for image_path in image_paths:
            # 获取CPU使用率
            cpu_percent = process.cpu_percent(interval=0.1)
            cpu_percentages.append(cpu_percent)
            
            # 执行特征提取
            extractor.extract_features(str(image_path))
        
        avg_cpu_usage = sum(cpu_percentages) / len(cpu_percentages)
        max_cpu_usage = max(cpu_percentages)
        
        # 验证CPU使用
        assert avg_cpu_usage < 80  # 平均CPU使用率小于80%
        assert max_cpu_usage < 95  # 最大CPU使用率小于95%
        
        print(f"CPU使用: 平均 {avg_cpu_usage:.1f}%, 最大 {max_cpu_usage:.1f}%")

    def test_scalability(self, test_images_dir, temp_dir):
        """测试系统可扩展性"""
        extractor = FeatureExtractor()
        search_engine = SearchEngine()
        
        extractor.register_extractor(ColorExtractor())
        
        # 测试不同数据规模
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        scalability_results = []
        
        for scale_factor in [1, 2, 4]:  # 测试不同规模
            # 复制图像路径来模拟更大数据集
            scaled_paths = image_paths * scale_factor
            
            start_time = time.time()
            
            # 处理数据
            features_data = {}
            for i, image_path in enumerate(scaled_paths):
                # 为复制的路径创建唯一标识
                unique_path = f"{image_path}_{i}"
                features = extractor.extract_features(str(image_path))
                features_data[unique_path] = features
            
            # 构建索引
            search_engine.build_index(features_data)
            
            # 执行搜索
            query_features = features_data[list(features_data.keys())[0]]
            results = search_engine.search(query_features, top_k=5)
            
            total_time = time.time() - start_time
            
            scalability_results.append({
                'scale': scale_factor,
                'data_size': len(scaled_paths),
                'time': total_time,
                'throughput': len(scaled_paths) / total_time
            })
        
        # 验证可扩展性
        for i in range(1, len(scalability_results)):
            # 吞吐量不应该线性下降
            throughput_ratio = (scalability_results[i]['throughput'] / 
                              scalability_results[i-1]['throughput'])
            assert throughput_ratio > 0.5  # 吞吐量下降不超过50%
        
        print("可扩展性测试结果:")
        for result in scalability_results:
            print(f"规模 {result['scale']}: {result['throughput']:.2f} 图像/秒") 