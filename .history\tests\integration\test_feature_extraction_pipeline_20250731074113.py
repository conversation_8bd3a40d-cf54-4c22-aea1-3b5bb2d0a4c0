"""
特征提取管道集成测试
"""

import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch

from features.core.feature_extractor import FeatureExtractor
from features.extractors.color_extractor import ColorExtractor
from features.extractors.texture_extractor import TextureExtractor
from features.extractors.shape_extractor import ShapeExtractor
from features.storage.feature_storage import FeatureStorage
from features.index.feature_index import FeatureIndex


class TestFeatureExtractionPipeline:
    """特征提取管道集成测试类"""

    def test_complete_pipeline(self, test_images_dir, temp_dir):
        """测试完整的特征提取管道"""
        # 初始化组件
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        index = FeatureIndex()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        # 获取测试图像列表
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        assert len(image_paths) > 0
        
        # 提取特征
        features_data = {}
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            features_data[str(image_path)] = features
            
            # 验证特征格式
            assert 'color' in features
            assert 'texture' in features
            assert 'shape' in features
        
        # 存储特征
        for image_path, features in features_data.items():
            storage.save_features(image_path, features)
        
        # 构建索引
        index.build_index(features_data)
        
        # 验证索引
        assert index.get_total_count() == len(image_paths)
        
        # 测试搜索
        query_features = features_data[str(image_paths[0])]
        results = index.search(query_features, top_k=3)
        
        assert len(results) > 0
        assert results[0]['similarity'] >= 0.8  # 自相似度应该很高

    def test_batch_processing_pipeline(self, test_images_dir, temp_dir):
        """测试批量处理管道"""
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        
        # 批量提取特征
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        batch_results = extractor.batch_extract_features(
            [str(p) for p in image_paths]
        )
        
        assert len(batch_results) == len(image_paths)
        
        # 批量存储
        for i, (image_path, features) in enumerate(batch_results):
            storage.save_features(image_path, features)
            
            # 验证存储
            loaded_features = storage.load_features(image_path)
            assert loaded_features is not None
            assert 'color' in loaded_features

    def test_feature_caching(self, test_images_dir, temp_dir):
        """测试特征缓存机制"""
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        
        image_path = str(list(Path(test_images_dir).glob("*.jpg"))[0])
        
        # 第一次提取（应该计算）
        with patch.object(ColorExtractor, 'extract_color_histogram') as mock_extract:
            mock_extract.return_value = [0.1, 0.2, 0.3]
            features1 = extractor.extract_features(image_path)
            assert mock_extract.called
        
        # 第二次提取（应该从缓存读取）
        with patch.object(ColorExtractor, 'extract_color_histogram') as mock_extract:
            features2 = extractor.extract_features(image_path)
            assert not mock_extract.called
        
        # 验证结果一致
        assert features1['color'] == features2['color']

    def test_index_update_pipeline(self, test_images_dir, temp_dir):
        """测试索引更新管道"""
        extractor = FeatureExtractor()
        index = FeatureIndex()
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 初始索引
        initial_features = {}
        for image_path in image_paths[:2]:  # 只处理前两个
            features = extractor.extract_features(str(image_path))
            initial_features[str(image_path)] = features
        
        index.build_index(initial_features)
        initial_count = index.get_total_count()
        
        # 添加新图像
        new_features = {}
        for image_path in image_paths[2:4]:  # 处理接下来的两个
            features = extractor.extract_features(str(image_path))
            new_features[str(image_path)] = features
        
        index.update_index(new_features)
        updated_count = index.get_total_count()
        
        assert updated_count == initial_count + len(new_features)

    def test_error_handling_pipeline(self, temp_dir):
        """测试错误处理管道"""
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        
        # 测试无效图像路径
        with patch('features.core.feature_extractor.load_image') as mock_load:
            mock_load.side_effect = FileNotFoundError("Image not found")
            
            # 应该优雅地处理错误
            result = extractor.extract_features('/invalid/path.jpg')
            assert result is None or 'error' in result

    def test_performance_pipeline(self, test_images_dir, temp_dir):
        """测试性能管道"""
        import time
        
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 测量提取时间
        start_time = time.time()
        for image_path in image_paths:
            extractor.extract_features(str(image_path))
        extraction_time = time.time() - start_time
        
        # 测量存储时间
        start_time = time.time()
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            storage.save_features(str(image_path), features)
        storage_time = time.time() - start_time
        
        # 验证性能指标
        avg_extraction_time = extraction_time / len(image_paths)
        avg_storage_time = storage_time / len(image_paths)
        
        # 每个图像的处理时间应该在合理范围内
        assert avg_extraction_time < 1.0  # 1秒内
        assert avg_storage_time < 0.1  # 0.1秒内

    def test_memory_usage_pipeline(self, test_images_dir, temp_dir):
        """测试内存使用管道"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        extractor = FeatureExtractor()
        storage = FeatureStorage(cache_dir=temp_dir)
        
        # 注册提取器
        extractor.register_extractor(ColorExtractor())
        extractor.register_extractor(TextureExtractor())
        extractor.register_extractor(ShapeExtractor())
        
        image_paths = list(Path(test_images_dir).glob("*.jpg"))
        
        # 处理所有图像
        for image_path in image_paths:
            features = extractor.extract_features(str(image_path))
            storage.save_features(str(image_path), features)
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于100MB）
        assert memory_increase < 100 * 1024 * 1024 