"""
Pytest配置文件

设置测试环境、共享fixtures和测试配置。
"""

import sys
import tempfile
import shutil
import pytest
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 测试配置
TEST_CONFIG = {
    'database': {
        'url': 'sqlite:///:memory:',
        'echo': False
    },
    'features': {
        'cache_dir': tempfile.mkdtemp(),
        'model_dir': tempfile.mkdtemp()
    },
    'search': {
        'top_k': 10,
        'similarity_threshold': 0.5
    },
    'api': {
        'host': '127.0.0.1',
        'port': 0  # 使用随机端口
    }
}

@pytest.fixture(scope="session")
def test_config():
    """测试配置fixture"""
    return TEST_CONFIG

@pytest.fixture(scope="session")
def temp_dir():
    """临时目录fixture"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)

@pytest.fixture(scope="session")
def test_images_dir(temp_dir):
    """测试图像目录fixture"""
    images_dir = Path(temp_dir) / "test_images"
    images_dir.mkdir(exist_ok=True)
    
    # 创建一些测试图像
    from PIL import Image
    import numpy as np
    
    for i in range(5):
        img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        img.save(images_dir / f"test_image_{i}.jpg")
    
    yield str(images_dir)
    shutil.rmtree(images_dir, ignore_errors=True)

@pytest.fixture(scope="function")
def mock_config_manager():
    """模拟配置管理器"""
    with patch('config.config_manager.ConfigManager') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.get_config.return_value = TEST_CONFIG
        yield mock_instance

@pytest.fixture(scope="function")
def mock_database_manager():
    """模拟数据库管理器"""
    with patch('database.database_manager.DatabaseManager') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.connect.return_value = True
        mock_instance.disconnect.return_value = True
        yield mock_instance

@pytest.fixture(scope="function")
def mock_feature_extractor():
    """模拟特征提取器"""
    with patch('features.core.feature_extractor.FeatureExtractor') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.extract_features.return_value = {
            'color': [0.1, 0.2, 0.3],
            'texture': [0.4, 0.5, 0.6],
            'shape': [0.7, 0.8, 0.9]
        }
        yield mock_instance

@pytest.fixture(scope="function")
def mock_search_engine():
    """模拟搜索引擎"""
    with patch('search.search_engine.SearchEngine') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.search.return_value = {
            'results': [
                {'image_path': '/path/to/image1.jpg', 'similarity': 0.95},
                {'image_path': '/path/to/image2.jpg', 'similarity': 0.85}
            ],
            'total_count': 2
        }
        yield mock_instance

@pytest.fixture(scope="session")
def flask_app():
    """Flask应用fixture"""
    from api.app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

@pytest.fixture(scope="function")
def client(flask_app):
    """Flask测试客户端fixture"""
    with flask_app.test_client() as client:
        yield client

# 性能测试配置
@pytest.fixture(scope="session")
def performance_config():
    """性能测试配置"""
    return {
        'test_duration': 60,  # 秒
        'concurrent_users': 10,
        'request_rate': 100,  # 请求/秒
        'timeout': 30,  # 秒
        'memory_limit': 1024 * 1024 * 1024,  # 1GB
        'cpu_limit': 80  # 百分比
    }

# 测试数据
@pytest.fixture(scope="session")
def sample_images():
    """样本图像数据"""
    return [
        {
            'path': '/path/to/image1.jpg',
            'features': {
                'color': [0.1, 0.2, 0.3, 0.4, 0.5],
                'texture': [0.6, 0.7, 0.8, 0.9, 1.0],
                'shape': [1.1, 1.2, 1.3, 1.4, 1.5]
            }
        },
        {
            'path': '/path/to/image2.jpg',
            'features': {
                'color': [0.2, 0.3, 0.4, 0.5, 0.6],
                'texture': [0.7, 0.8, 0.9, 1.0, 1.1],
                'shape': [1.2, 1.3, 1.4, 1.5, 1.6]
            }
        }
    ]

# 日志配置
@pytest.fixture(scope="session", autouse=True)
def setup_test_logging():
    """设置测试日志"""
    import logging
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ) 