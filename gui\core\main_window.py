#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口

该模块提供应用程序的主窗口界面。
重构后的主窗口使用管理器模式和核心类，将功能分散到各个专门的模块中。
"""

import os
import sys
from typing import Optional, List, Dict, Any

from PyQt6.QtWidgets import QMainWindow, QWidget, QSplitter, QApplication
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QCloseEvent, QResizeEvent, QMoveEvent

from .main_window_core import MainWindowCore
from ..search_panel import SearchPanel
from ..result_panel import ResultPanel
from ..image_viewer import ImageViewer
from ..common.gui_utils import GUIUtils


class MainWindow(QMainWindow):
    """主窗口
    
    重构后的主窗口使用核心类和管理器模式，将复杂的功能分散到专门的模块中。
    """
    
    # 信号
    searchRequested = pyqtSignal(dict)  # 搜索请求
    imageSelected = pyqtSignal(str)  # 图像选择
    settingsChanged = pyqtSignal(dict)  # 设置变更
    
    def __init__(self, parent=None, feature_manager=None, fabric_repository=None, 
                 search_repository=None, search_history_manager=None, 
                 statistics_manager=None, gpu_manager=None, model_config_manager=None):
        super().__init__(parent)
        
        # 添加调试日志
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"MainWindow.__init__ - feature_manager状态: {feature_manager is not None}")
        if feature_manager is not None:
            logger.info(f"MainWindow.__init__ - feature_manager类型: {type(feature_manager)}")
            logger.info(f"MainWindow.__init__ - feature_manager ID: {id(feature_manager)}")
        
        # 存储核心组件
        self.feature_manager = feature_manager
        self.fabric_repository = fabric_repository
        self.search_repository = search_repository
        self.search_history_manager = search_history_manager
        self.statistics_manager = statistics_manager
        self.gpu_manager = gpu_manager
        self.model_config_manager = model_config_manager
        
        # 初始化核心功能
        self.core = MainWindowCore(self, feature_manager=feature_manager)
        
        # UI组件
        self.search_panel: Optional[SearchPanel] = None
        self.result_panel: Optional[ResultPanel] = None
        self.image_viewer: Optional[ImageViewer] = None
        self.main_splitter: Optional[QSplitter] = None
        self.right_splitter: Optional[QSplitter] = None
        
        # 设置窗口和UI
        self._setup_window()
        self._setup_ui()
        self._connect_signals()
        
        # 加载设置
        self.core.load_and_apply_settings()
        
        # 只有在feature_manager存在时才初始化搜索引擎
        # 避免重复创建feature_manager
        if self.feature_manager is not None:
            self.core.init_search_engine()
    
    @property
    def menu_manager(self):
        """菜单管理器"""
        return self.core.menu_manager
    
    @property
    def toolbar_manager(self):
        """工具栏管理器"""
        return self.core.toolbar_manager
    
    @property
    def search_handler(self):
        """搜索处理器"""
        return self.core.search_handler
    
    @property
    def file_handler(self):
        """文件处理器"""
        return self.core.file_handler
    
    @property
    def settings_manager(self):
        """设置管理器"""
        return self.core.settings_manager
    
    @property
    def event_handler(self):
        """事件处理器"""
        return self.core.event_handler
    
    @property
    def window_state_manager(self):
        """窗口状态管理器"""
        return self.core.window_state_manager
    
    @property
    def task_manager(self):
        """任务管理器"""
        return self.core.task_manager
    
    def _setup_window(self):
        """设置窗口"""
        self.setWindowTitle("Fabric Search - 图像搜索工具")
        self.setMinimumSize(1000, 700)
        self.resize(1400, 900)
        
        # 设置窗口图标
        icon_path = os.path.join(
            os.path.dirname(__file__), 
            "../../resources/icons/app_icon.png"
        )
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 居中显示
        GUIUtils.center_window(self)
    
    def _setup_ui(self):
        """设置界面"""
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 使用核心类设置UI
        self.main_splitter = self.core.setup_ui(central_widget)
        
        # 左侧面板（搜索面板）
        self.search_panel = SearchPanel()
        self.search_panel.setMinimumWidth(300)
        self.search_panel.setMaximumWidth(400)
        self.main_splitter.addWidget(self.search_panel)
        
        # 右侧分割器
        self.right_splitter = QSplitter(Qt.Orientation.Vertical)
        self.main_splitter.addWidget(self.right_splitter)
        
        # 结果面板
        self.result_panel = ResultPanel()
        self.right_splitter.addWidget(self.result_panel)
        
        # 图像查看器
        self.image_viewer = ImageViewer()
        self.image_viewer.setMinimumHeight(200)
        self.right_splitter.addWidget(self.image_viewer)
        
        # 设置分割器比例
        self.main_splitter.setSizes([300, 1100])
        self.right_splitter.setSizes([500, 300])
    
    def _connect_signals(self):
        """连接信号"""
        # 使用核心类连接所有管理器信号
        self.core.connect_all_signals()
        
        # 连接UI组件信号
        if self.search_panel:
            self.search_panel.searchRequested.connect(
                self._on_search_requested
            )
            self.search_panel.searchStopped.connect(
                self.search_handler.stop_search
            )
            # 连接文件夹路径设置信号
            self.search_panel.folderPathSet.connect(
                self._on_folder_path_set_for_extraction
            )
        
        if self.result_panel:
            self.result_panel.itemSelected.connect(
                self.event_handler.handle_item_selection
            )
            self.result_panel.itemDoubleClicked.connect(
                self.event_handler.handle_item_double_click
            )
            self.result_panel.itemSelected.connect(
                self._on_item_selected
            )
            self.result_panel.itemDoubleClicked.connect(
                self._on_item_double_clicked
            )
    
    # 事件处理方法（委托给核心类）
    def _on_search_requested(self, query: str, params: dict):
        """处理搜索请求
        
        Args:
            query: 搜索查询（图像路径或文本查询）
            params: 搜索参数
        """
        # 从当前搜索面板获取搜索模式
        search_mode = self.search_panel.get_current_mode() if self.search_panel else None
        
        # 创建搜索配置
        from gui.search.models import SearchConfig
        search_config = SearchConfig(mode=search_mode)
        
        # 设置查询参数
        # 添加查询属性（如果不存在）
        if not hasattr(search_config, 'query'):
            search_config.query = query
        
        # 合并参数
        for key, value in params.items():
            setattr(search_config, key, value)
        
        # 启动搜索
        self.search_handler.start_search(search_config)
    
    def _on_item_selected(self, item_path: str):
        """项目选中"""
        if self.image_viewer:
            self.image_viewer.load_image(item_path)
    
    def _on_item_double_clicked(self, item_path: str):
        """项目双击"""
        # 可以在这里添加双击处理逻辑
        pass
    
    def _on_search_started(self):
        """搜索开始"""
        self.core._on_search_started()
        if self.search_panel:
            self.search_panel.set_searching(True)
    
    def _on_search_finished(self):
        """搜索完成"""
        self.core._on_search_finished()
        if self.search_panel:
            self.search_panel.set_searching(False)
    
    def _on_search_error(self, error_message: str):
        """搜索错误"""
        self.core._on_search_error(error_message)
        if self.search_panel:
            self.search_panel.set_searching(False)
    
    def _on_search_results_updated(self, results: List[Dict[str, Any]]):
        """搜索结果更新"""
        self.core._on_search_results_updated(results)
        if self.result_panel:
            self.result_panel.update_results(results)
    
    def _on_search_progress(self, progress: int, message: str):
        """搜索进度更新"""
        self.core._on_search_progress(progress, message)
    
    def _on_image_opened(self, image_path: str):
        """图像打开"""
        self.core._on_image_opened(image_path)
        if self.search_panel:
            self.search_panel.set_image_path(image_path)
        if self.image_viewer:
            self.image_viewer.load_image(image_path)
    
    def _on_folder_opened(self, folder_path: str):
        """文件夹打开"""
        self.core._on_folder_opened(folder_path)
        if self.search_panel:
            self.search_panel.set_folder_path(folder_path)
    
    def _on_folder_path_set_for_extraction(self, folder_path: str):
        """处理文件夹路径设置，进行特征提取
        
        Args:
            folder_path: 文件夹路径
        """
        try:
            # 使用核心类的logger
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.info(f"开始处理文件夹特征提取: {folder_path}")
            
            # 更新状态
            self.core.window_state_manager.update_status("正在扫描文件夹...")
            
            # 扫描文件夹中的图像文件
            from utils.file_utils import scan_image_folder
            image_files = scan_image_folder(folder_path, recursive=True)
            
            if not image_files:
                from gui.helpers.message_helper import MessageHelper
                MessageHelper.show_warning(self, "警告", 
                    f"文件夹中没有找到有效的图像文件: {folder_path}\n"
                    f"支持的格式: jpg, jpeg, png, bmp, gif, tiff, webp")
                self.core.window_state_manager.update_status("扫描完成，未找到图像文件")
                return
            
            # 提取图像路径
            image_paths = [file_info['path'] for file_info in image_files]
            
            if logger:
                logger.info(f"找到 {len(image_paths)} 个有效图像文件，开始特征提取")
            
            # 更新状态显示文件数量
            self.core.window_state_manager.update_status(
                f"找到 {len(image_paths)} 个图像文件，准备提取特征..."
            )
            
            # 启动特征提取任务
            self._start_feature_extraction(image_paths, folder_path)
            
        except Exception as e:
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.error(f"处理文件夹特征提取失败: {e}")
            from gui.helpers.message_helper import MessageHelper
            MessageHelper.show_error(self, "错误", f"处理文件夹失败: {e}")
            self.core.window_state_manager.update_status(f"处理失败: {e}")
    
    def _start_feature_extraction(self, image_paths: list, folder_path: str):
        """启动特征提取任务
        
        Args:
            image_paths: 图像路径列表
            folder_path: 文件夹路径
        """
        try:
            # 获取特征管理器
            feature_manager = self.feature_manager
            if not feature_manager:
                logger = getattr(self.core, 'logger', None)
                if logger:
                    logger.error("特征管理器未初始化")
                from gui.helpers.message_helper import MessageHelper
                MessageHelper.show_error(self, "错误", "特征管理器未初始化")
                return
            
            # 创建任务
            task_name = f"特征提取 - {len(image_paths)} 个图像"
            task_metadata = {
                'type': 'feature_extraction',
                'image_count': len(image_paths),
                'folder_path': folder_path
            }
            task_id = self.task_manager.create_task(task_name, task_metadata)
            
            # 更新状态
            self.core.window_state_manager.update_status(f"正在提取 {len(image_paths)} 个图像的特征...")
            self.core.window_state_manager.show_progress()
            
            # 启动特征提取（在后台线程中）
            from PyQt6.QtCore import QThread, QObject, pyqtSignal
            
            class FeatureExtractionWorker(QObject):
                finished = pyqtSignal()
                error = pyqtSignal(str)
                progress = pyqtSignal(int, str)
                
                def __init__(self, feature_manager, image_paths, task_manager, task_id):
                    super().__init__()
                    self.feature_manager = feature_manager
                    self.image_paths = image_paths
                    self.task_manager = task_manager
                    self.task_id = task_id
                
                def run(self):
                    try:
                        # 更新任务状态为运行中
                        self.task_manager.update_task_status(self.task_id, "running")
                        
                        # 执行特征提取，并更新进度
                        total_images = len(self.image_paths)
                        processed_count = 0
                        
                        # 分批处理图像以更新进度
                        batch_size = max(1, min(total_images // 50, 10))  # 分成更多批次，但每批不超过10个图像
                        
                        for i in range(0, total_images, batch_size):
                            batch_paths = self.image_paths[i:i + batch_size]
                            
                            # 处理当前批次
                            result = self.feature_manager.extract_and_store_features(batch_paths)
                            
                            if result.success:
                                processed_count += result.processed
                            else:
                                # 如果批次失败，记录错误但继续处理
                                logger = getattr(self.feature_manager, 'logger', None)
                                if logger:
                                    logger.warning(f"批次处理失败: {result.error}")
                            
                            # 检查任务是否被取消
                            if self.task_manager.get_task_status(self.task_id) == "cancelled":
                                break
                                
                            # 更新进度
                            progress = min(processed_count / total_images, 1.0)
                            progress_percent = int(progress * 100)
                            message = f"已处理 {processed_count}/{total_images} 个图像"
                            
                            try:
                                self.progress.emit(progress_percent, message)
                                self.task_manager.update_task_progress(self.task_id, progress)
                            except RuntimeError:
                                # 如果对象已被删除，停止处理
                                break
                        
                        # 完成任务
                        try:
                            if processed_count > 0:
                                self.progress.emit(100, f"特征提取完成，成功处理 {processed_count} 个文件")
                                self.task_manager.complete_task(self.task_id, {
                                    'processed': processed_count,
                                    'total_count': total_images
                                })
                            else:
                                error_msg = "特征提取失败：没有成功处理任何图像"
                                self.error.emit(error_msg)
                                self.task_manager.complete_task(self.task_id, error=error_msg)
                            
                            self.finished.emit()
                        except RuntimeError:
                            # 如果对象已被删除，只完成任务但不发送信号
                            self.task_manager.complete_task(self.task_id, {
                                'processed': processed_count,
                                'total_count': total_images
                            })
                        
                    except Exception as e:
                        error_msg = f"特征提取过程中发生错误: {str(e)}"
                        try:
                            self.error.emit(error_msg)
                            self.task_manager.complete_task(self.task_id, error=error_msg)
                            self.finished.emit()
                        except RuntimeError:
                            # 如果对象已被删除，只完成任务但不发送信号
                            self.task_manager.complete_task(self.task_id, error=error_msg)
            
            # 创建工作线程
            self.extraction_thread = QThread()
            self.extraction_worker = FeatureExtractionWorker(feature_manager, image_paths, self.task_manager, task_id)
            self.extraction_worker.moveToThread(self.extraction_thread)
            
            # 连接信号
            self.extraction_thread.started.connect(self.extraction_worker.run)
            self.extraction_worker.finished.connect(self.extraction_thread.quit)
            self.extraction_worker.finished.connect(self._on_extraction_finished)
            self.extraction_worker.finished.connect(self.extraction_worker.deleteLater)  # 安全删除工作对象
            self.extraction_thread.finished.connect(self.extraction_thread.deleteLater)  # 安全删除线程
            self.extraction_worker.error.connect(self._on_extraction_error)
            self.extraction_worker.progress.connect(self._on_extraction_progress)
            
            # 启动线程
            self.extraction_thread.start()
            
        except Exception as e:
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.error(f"启动特征提取任务失败: {e}")
            from gui.helpers.message_helper import MessageHelper
            MessageHelper.show_error(self, "错误", f"启动特征提取失败: {e}")
    
    def _on_extraction_finished(self):
        """特征提取完成"""
        try:
            self.core.window_state_manager.hide_progress()
            self.core.window_state_manager.update_status("特征提取完成")
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.info("文件夹特征提取任务完成")
            
        except Exception as e:
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.error(f"处理特征提取完成事件失败: {e}")
    
    def _on_extraction_error(self, error_message: str):
        """特征提取错误"""
        try:
            self.core.window_state_manager.hide_progress()
            self.core.window_state_manager.update_status(f"特征提取失败: {error_message}")
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.error(f"特征提取失败: {error_message}")
            
            from gui.helpers.message_helper import MessageHelper
            MessageHelper.show_error(self, "特征提取失败", error_message)
            
        except Exception as e:
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.error(f"处理特征提取错误事件失败: {e}")
    
    def _on_extraction_progress(self, progress: int, message: str):
        """特征提取进度更新"""
        try:
            self.core.window_state_manager.update_progress(progress, message)
            
        except Exception as e:
            logger = getattr(self.core, 'logger', None)
            if logger:
                logger.error(f"更新特征提取进度失败: {e}")
    
    def _on_database_imported(self, database_path: str):
        """数据库导入完成"""
        self.core._on_database_imported(database_path)
    
    def _on_results_exported(self, export_path: str):
        """结果导出完成"""
        self.core._on_results_exported(export_path)
    
    def _on_file_operation_error(self, error_message: str):
        """文件操作错误"""
        self.core._on_file_operation_error(error_message)
    
    def _on_settings_loaded(self, settings: Dict[str, Any]):
        """设置加载完成"""
        self.core._on_settings_loaded(settings)
    
    def _on_settings_saved(self):
        """设置保存完成"""
        self.core._on_settings_saved()
    
    def _on_settings_changed(self, key: str, value: Any):
        """设置变更"""
        self.core._on_settings_changed(key, value)
    
    def _on_settings_applied(self, settings: Dict[str, Any]):
        """设置应用"""
        self.core._on_settings_applied(settings)
    
    def _on_font_changed(self, font):
        """字体变更"""
        self.core._on_font_changed(font)
    
    def _on_theme_changed(self, theme_type):
        """主题变更"""
        self.core._on_theme_changed(theme_type)
    
    def _on_model_changed(self, model_path: str):
        """模型变更"""
        self.core._on_model_changed(model_path)
    
    def _on_fullscreen_changed(self, is_fullscreen: bool):
        """全屏状态变更"""
        self.core._on_fullscreen_changed(is_fullscreen)
    
    def _on_toolbar_visibility_changed(self, is_visible: bool):
        """工具栏可见性变更"""
        self.core._on_toolbar_visibility_changed(is_visible)
    
    def _on_status_updated(self, message: str):
        """状态更新"""
        self.core._on_status_updated(message)
    
    # Qt事件处理
    def closeEvent(self, event: QCloseEvent):
        """关闭事件"""
        if self.core.handle_close_event(event):
            event.accept()
        else:
            event.ignore()
    
    def resizeEvent(self, event: QResizeEvent):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.core.handle_resize_event(event)
    
    def moveEvent(self, event: QMoveEvent):
        """窗口移动事件"""
        super().moveEvent(event)
        self.core.handle_move_event(event)
    
    def compare_images(self, image1_path: str, image2_path: str = None):
        """对比图像"""
        self.event_handler.compare_images(image1_path, image2_path)
    
    def get_search_image(self) -> Optional[str]:
        """获取搜索图像路径"""
        if self.search_panel:
            return self.search_panel.get_image_path()
        return None