"""特征缓存管理模块

提供特征数据的内存缓存功能，包括线程安全的缓存操作。
"""

import threading
from typing import Dict, Any, Optional, List
import numpy as np
from pathlib import Path
import pickle
import logging


class FeatureCache:
    """特征缓存管理器
    
    提供线程安全的特征缓存功能，支持：
    - 特征的存储和检索
    - 缓存大小限制
    - LRU淘汰策略
    - 缓存统计信息
    """
    
    def __init__(self, max_size: int = 10000):
        """初始化特征缓存
        
        Args:
            max_size: 最大缓存条目数
        """
        self.max_size = max_size
        self._cache: Dict[Any, np.ndarray] = {}
        self._access_order: List[Any] = []  # LRU访问顺序
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self._hits = 0
        self._misses = 0
        
    def get(self, key: Any) -> Optional[np.ndarray]:
        """获取缓存的特征
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[np.ndarray]: 特征数组，如果不存在返回None
        """
        with self._lock:
            if key in self._cache:
                # 更新访问顺序
                self._access_order.remove(key)
                self._access_order.append(key)
                self._hits += 1
                return self._cache[key].copy()  # 返回副本避免修改
            else:
                self._misses += 1
                return None
                
    def put(self, key: Any, features: np.ndarray) -> bool:
        """存储特征到缓存
        
        Args:
            key: 缓存键
            features: 特征数组
            
        Returns:
            bool: 是否成功存储
        """
        try:
            if not isinstance(features, np.ndarray):
                self.logger.warning(f"特征类型无效: {type(features)}")
                return False
                
            if len(features) == 0:
                self.logger.warning("特征数组为空")
                return False
                
            # 检查特征是否包含NaN或无穷大值
            if np.isnan(features).any() or np.isinf(features).any():
                self.logger.warning("特征包含NaN或无穷大值")
                return False
                
            with self._lock:
                # 如果键已存在，更新访问顺序
                if key in self._cache:
                    self._access_order.remove(key)
                    self._access_order.append(key)
                    self._cache[key] = features.copy()
                    return True
                    
                # 检查缓存大小限制
                if len(self._cache) >= self.max_size:
                    self._evict_lru()
                    
                # 添加新条目
                self._cache[key] = features.copy()
                self._access_order.append(key)
                return True
                
        except Exception as e:
            self.logger.error(f"存储特征到缓存失败: {e}")
            return False
            
    def remove(self, key: Any) -> bool:
        """从缓存中移除特征
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._access_order.remove(key)
                return True
            return False
            
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._hits = 0
            self._misses = 0
            
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的条目"""
        if self._access_order:
            lru_key = self._access_order.pop(0)
            del self._cache[lru_key]
            
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
        
    def hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self._hits + self._misses
        return self._hits / total if total > 0 else 0.0
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': self.hit_rate(),
                'memory_usage_mb': self._estimate_memory_usage()
            }
            
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        total_bytes = 0
        for features in self._cache.values():
            total_bytes += features.nbytes
        return total_bytes / (1024 * 1024)
        
    def save_to_disk(self, file_path: Path) -> bool:
        """将缓存保存到磁盘
        
        Args:
            file_path: 保存路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            with self._lock:
                cache_data = {
                    'cache': dict(self._cache),
                    'access_order': list(self._access_order),
                    'hits': self._hits,
                    'misses': self._misses
                }
                
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'wb') as f:
                pickle.dump(cache_data, f)
                
            self.logger.info(f"缓存保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
            return False
            
    def load_from_disk(self, file_path: Path) -> bool:
        """从磁盘加载缓存
        
        Args:
            file_path: 缓存文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            if not file_path.exists():
                self.logger.warning(f"缓存文件不存在: {file_path}")
                return False
                
            with open(file_path, 'rb') as f:
                cache_data = pickle.load(f)
                
            with self._lock:
                self._cache = cache_data.get('cache', {})
                self._access_order = cache_data.get('access_order', [])
                self._hits = cache_data.get('hits', 0)
                self._misses = cache_data.get('misses', 0)
                
            self.logger.info(f"缓存加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
            return False
            
    def contains(self, key: Any) -> bool:
        """检查缓存中是否包含指定键
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否包含
        """
        with self._lock:
            return key in self._cache
            
    def keys(self) -> List[Any]:
        """获取所有缓存键
        
        Returns:
            List[Any]: 缓存键列表
        """
        with self._lock:
            return list(self._cache.keys())