#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

提供系统配置的统一管理，包括配置文件读取、验证、更新和环境变量支持。
支持多种配置格式（JSON、YAML、TOML）和配置热重载。
"""

import os
import json
import configparser
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from dataclasses import dataclass, field, asdict
from datetime import datetime
import threading
import time

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import toml
    TOML_AVAILABLE = True
except ImportError:
    TOML_AVAILABLE = False

from utils.log_utils import setup_logging
from utils.logger_mixin import LoggerMixin


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = 'localhost'
    port: int = 5432
    database: str = 'fabric_search'
    username: str = 'postgres'
    password: str = ''
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    
    def get_url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


@dataclass
class FeatureExtractionConfig:
    """特征提取配置"""
    model_name: str = 'resnet50'
    model_path: Optional[str] = None
    device: str = 'auto'  # 'auto', 'cpu', 'cuda', 'cuda:0'
    batch_size: int = 32
    image_size: tuple = (224, 224)
    normalize: bool = True
    use_mixed_precision: bool = True
    cache_features: bool = True
    feature_dim: int = 2048
    
    # 预处理配置
    resize_method: str = 'bilinear'  # 'bilinear', 'bicubic', 'nearest'
    center_crop: bool = True
    random_crop: bool = False
    horizontal_flip: bool = False
    color_jitter: bool = False
    
    # 高级特征（传统特征）配置
    extract_color: bool = True
    extract_texture: bool = True
    extract_shape: bool = True
    
    # 颜色特征参数
    hist_bins: int = 32
    n_dominant_colors: int = 5
    
    # 纹理特征参数
    lbp_radius: int = 3
    lbp_n_points: int = 24
    
    # 形状特征参数
    n_fourier_descriptors: int = 32
    
    # 性能配置
    num_workers: int = 4
    pin_memory: bool = True
    prefetch_factor: int = 2


@dataclass
class SearchConfig:
    """搜索配置"""
    index_type: str = 'faiss'  # 'faiss', 'cosine', 'euclidean'
    similarity_metric: str = 'cosine'  # 'cosine', 'euclidean', 'manhattan'
    top_k: int = 10
    similarity_threshold: float = 0.0
    use_gpu: bool = True
    
    # 路径配置
    data_dir: str = 'data'
    
    # FAISS配置
    faiss_index_type: str = 'IndexFlatIP'  # 'IndexFlatIP', 'IndexIVFFlat', 'IndexHNSW'
    faiss_nlist: int = 100
    faiss_nprobe: int = 10
    faiss_m: int = 16
    faiss_ef_construction: int = 200
    faiss_ef_search: int = 50
    
    # 缓存配置
    cache_results: bool = True
    cache_size: int = 1000
    cache_ttl: int = 3600  # 秒


@dataclass
class BatchProcessingConfig:
    """批处理配置"""
    max_workers: int = 4
    batch_size: int = 32
    queue_size: int = 1000
    timeout: int = 300
    retry_attempts: int = 3
    retry_delay: float = 1.0
    
    # 进度监控
    progress_update_interval: float = 1.0
    save_progress: bool = True
    progress_file: str = 'batch_progress.json'
    
    # 错误处理
    stop_on_error: bool = False
    max_errors: int = 100
    error_log_file: str = 'batch_errors.log'
    
    # 性能优化
    use_multiprocessing: bool = True
    chunk_size: int = 100
    memory_limit: Optional[int] = None  # MB


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format: str = '%Y-%m-%d %H:%M:%S'
    
    # 文件日志
    log_to_file: bool = True
    log_file: str = 'fabric_search.log'
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # 控制台日志
    log_to_console: bool = True
    console_level: str = 'INFO'
    
    # 特殊日志
    sql_log: bool = False
    performance_log: bool = True
    error_log: bool = True


@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    backend: str = 'memory'  # 'memory', 'redis', 'file'
    
    # 内存缓存
    max_size: int = 1000
    ttl: int = 3600
    
    # Redis缓存
    redis_host: str = 'localhost'
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # 文件缓存
    cache_dir: str = 'cache'
    max_disk_size: int = 1024 * 1024 * 1024  # 1GB


@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = ''
    api_key_required: bool = False
    rate_limit_enabled: bool = True
    max_requests_per_minute: int = 100
    
    # 文件上传安全
    allowed_extensions: List[str] = field(default_factory=lambda: ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'])
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    scan_uploads: bool = True
    
    # 路径安全
    allowed_paths: List[str] = field(default_factory=list)
    blocked_paths: List[str] = field(default_factory=list)


@dataclass
class PerformanceConfig:
    """性能配置"""
    # GPU配置
    gpu_memory_fraction: float = 0.8
    allow_memory_growth: bool = True
    
    # 并发配置
    max_concurrent_requests: int = 10
    request_timeout: int = 30
    
    # 优化配置
    enable_profiling: bool = False
    profile_output_dir: str = 'profiles'
    
    # 监控配置
    monitor_system_resources: bool = True
    resource_check_interval: int = 60


@dataclass
class APIConfig:
    """API服务器配置"""
    host: str = '127.0.0.1'
    port: int = 5000
    debug: bool = False
    threaded: bool = True
    
    # CORS配置
    cors_enabled: bool = True
    cors_origins: List[str] = field(default_factory=lambda: ['*'])
    
    # API文档配置
    enable_docs: bool = True
    docs_path: str = '/api/docs'
    
    # 请求限制
    max_content_length: int = 16 * 1024 * 1024  # 16MB
    request_timeout: int = 30


@dataclass
class SystemConfig:
    """系统总配置"""
    # 基本信息
    app_name: str = 'Fabric Search'
    version: str = '2.0.0'
    debug: bool = False
    
    # 路径配置
    data_dir: str = 'data'
    model_dir: str = 'models'
    cache_dir: str = 'cache'
    log_dir: str = 'logs'
    temp_dir: str = 'temp'
    
    # 子配置
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    feature_extraction: FeatureExtractionConfig = field(default_factory=FeatureExtractionConfig)
    search: SearchConfig = field(default_factory=SearchConfig)
    batch_processing: BatchProcessingConfig = field(default_factory=BatchProcessingConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    api: APIConfig = field(default_factory=APIConfig)


class ConfigManager(LoggerMixin):
    """配置管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config_file: Optional[str] = None):
        if hasattr(self, '_initialized'):
            return
            
        super().__init__()
        self._initialized = True
        
        self.config_file = config_file or self._find_config_file()
        self.config = SystemConfig()
        self._watchers = []
        self._last_modified = None
        
        # 加载配置
        self.load_config()
        
        # 启动配置监控
        if self.config_file and os.path.exists(self.config_file):
            self._start_file_watcher()
    
    def _find_config_file(self) -> Optional[str]:
        """查找配置文件"""
        possible_files = [
            'config.yaml',
            'config.yml',
            'config.json',
            'config.toml',
            'config.ini',
            'fabric_search.yaml',
            'fabric_search.yml',
            'fabric_search.json'
        ]
        
        # 在当前目录和上级目录中查找
        search_dirs = ['.', '..', 'config', '../config']
        
        for directory in search_dirs:
            for filename in possible_files:
                filepath = os.path.join(directory, filename)
                if os.path.exists(filepath):
                    return filepath
        
        return None
    
    def load_config(self, config_file: Optional[str] = None) -> bool:
        """加载配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if config_file:
                self.config_file = config_file
            
            # 首先从环境变量加载
            self._load_from_env()
            
            # 然后从配置文件加载
            if self.config_file and os.path.exists(self.config_file):
                self._load_from_file(self.config_file)
                self._last_modified = os.path.getmtime(self.config_file)
            
            # 验证配置
            self._validate_config()
            
            # 创建必要的目录
            self._create_directories()
            
            self.logger.info(f"配置加载成功: {self.config_file or '环境变量'}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            return False
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv('DB_HOST'):
            self.config.database.host = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            self.config.database.port = int(os.getenv('DB_PORT'))
        if os.getenv('DB_NAME'):
            self.config.database.database = os.getenv('DB_NAME')
        if os.getenv('DB_USER'):
            self.config.database.username = os.getenv('DB_USER')
        if os.getenv('DB_PASSWORD'):
            self.config.database.password = os.getenv('DB_PASSWORD')
        
        # 基本配置
        if os.getenv('DEBUG'):
            self.config.debug = os.getenv('DEBUG').lower() in ('true', '1', 'yes')
        if os.getenv('LOG_LEVEL'):
            self.config.logging.level = os.getenv('LOG_LEVEL')
        
        # GPU配置
        if os.getenv('CUDA_VISIBLE_DEVICES'):
            self.config.feature_extraction.device = f"cuda:{os.getenv('CUDA_VISIBLE_DEVICES')}"
        
        # 安全配置
        if os.getenv('SECRET_KEY'):
            self.config.security.secret_key = os.getenv('SECRET_KEY')
    
    def _load_from_file(self, config_file: str):
        """从文件加载配置"""
        file_ext = Path(config_file).suffix.lower()
        
        if file_ext in ['.yaml', '.yml']:
            self._load_yaml(config_file)
        elif file_ext == '.json':
            self._load_json(config_file)
        elif file_ext == '.toml':
            self._load_toml(config_file)
        elif file_ext == '.ini':
            self._load_ini(config_file)
        else:
            raise ValueError(f"不支持的配置文件格式: {file_ext}")
    
    def _load_yaml(self, config_file: str):
        """加载YAML配置"""
        if not YAML_AVAILABLE:
            raise ImportError("PyYAML未安装，无法加载YAML配置")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        self._update_config_from_dict(data)
    
    def _load_json(self, config_file: str):
        """加载JSON配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self._update_config_from_dict(data)
    
    def _load_toml(self, config_file: str):
        """加载TOML配置"""
        if not TOML_AVAILABLE:
            raise ImportError("toml未安装，无法加载TOML配置")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            data = toml.load(f)
        
        self._update_config_from_dict(data)
    
    def _load_ini(self, config_file: str):
        """加载INI配置"""
        config_parser = configparser.ConfigParser()
        config_parser.read(config_file, encoding='utf-8')
        
        # 转换为字典格式
        data = {}
        for section_name in config_parser.sections():
            section = config_parser[section_name]
            data[section_name] = dict(section)
        
        self._update_config_from_dict(data)
    
    def _update_config_from_dict(self, data: Dict[str, Any]):
        """从字典更新配置"""
        def update_dataclass(obj, updates):
            if not isinstance(updates, dict):
                return
            
            for key, value in updates.items():
                if hasattr(obj, key):
                    attr = getattr(obj, key)
                    if hasattr(attr, '__dataclass_fields__'):  # 是dataclass
                        update_dataclass(attr, value)
                    else:
                        # 类型转换
                        if isinstance(value, str) and hasattr(attr, '__class__'):
                            if attr.__class__ == bool:
                                value = value.lower() in ('true', '1', 'yes')
                            elif attr.__class__ == int:
                                value = int(value)
                            elif attr.__class__ == float:
                                value = float(value)
                        
                        setattr(obj, key, value)
        
        update_dataclass(self.config, data)
    
    def _validate_config(self):
        """验证配置"""
        # 验证数据库配置
        if not self.config.database.host:
            raise ValueError("数据库主机不能为空")
        
        if not self.config.database.database:
            raise ValueError("数据库名称不能为空")
        
        # 验证特征提取配置
        if self.config.feature_extraction.batch_size <= 0:
            raise ValueError("批处理大小必须大于0")
        
        # 验证搜索配置
        if self.config.search.top_k <= 0:
            raise ValueError("top_k必须大于0")
        
        # 验证日志配置
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.config.logging.level not in valid_levels:
            raise ValueError(f"无效的日志级别: {self.config.logging.level}")
    
    def _create_directories(self):
        """创建必要的目录"""
        dirs_to_create = [
            self.config.data_dir,
            self.config.model_dir,
            self.config.cache_dir,
            self.config.log_dir,
            self.config.temp_dir
        ]
        
        for directory in dirs_to_create:
            os.makedirs(directory, exist_ok=True)
    
    def _start_file_watcher(self):
        """启动文件监控"""
        def watch_file():
            while True:
                try:
                    if os.path.exists(self.config_file):
                        current_modified = os.path.getmtime(self.config_file)
                        if self._last_modified and current_modified > self._last_modified:
                            self.logger.info("检测到配置文件变化，重新加载配置")
                            self.load_config()
                            self._notify_watchers()
                    
                    time.sleep(1)  # 每秒检查一次
                    
                except Exception as e:
                    self.logger.error(f"配置文件监控错误: {e}")
                    time.sleep(5)
        
        watcher_thread = threading.Thread(target=watch_file, daemon=True)
        watcher_thread.start()
    
    def save_config(self, config_file: Optional[str] = None) -> bool:
        """保存配置到文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if config_file:
                self.config_file = config_file
            
            if not self.config_file:
                self.config_file = 'config.yaml'
            
            # 转换为字典
            config_dict = asdict(self.config)
            
            # 根据文件扩展名保存
            file_ext = Path(self.config_file).suffix.lower()
            
            if file_ext in ['.yaml', '.yml']:
                self._save_yaml(config_dict)
            elif file_ext == '.json':
                self._save_json(config_dict)
            elif file_ext == '.toml':
                self._save_toml(config_dict)
            else:
                # 默认保存为YAML
                self.config_file = str(Path(self.config_file).with_suffix('.yaml'))
                self._save_yaml(config_dict)
            
            self.logger.info(f"配置已保存到: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def _save_yaml(self, config_dict: Dict[str, Any]):
        """保存为YAML格式"""
        if not YAML_AVAILABLE:
            raise ImportError("PyYAML未安装，无法保存YAML配置")
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
    
    def _save_json(self, config_dict: Dict[str, Any]):
        """保存为JSON格式"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def _save_toml(self, config_dict: Dict[str, Any]):
        """保存为TOML格式"""
        if not TOML_AVAILABLE:
            raise ImportError("toml未安装，无法保存TOML配置")
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            toml.dump(config_dict, f)
    
    def get_config(self) -> SystemConfig:
        """获取配置对象"""
        return self.config
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                value = getattr(value, k)
            
            return value
            
        except (AttributeError, KeyError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            keys = key.split('.')
            obj = self.config
            
            # 导航到父对象
            for k in keys[:-1]:
                obj = getattr(obj, k)
            
            # 设置值
            setattr(obj, keys[-1], value)
            
            self.logger.debug(f"配置已更新: {key} = {value}")
            return True
            
        except (AttributeError, KeyError) as e:
            self.logger.error(f"设置配置失败: {key} = {value}, 错误: {e}")
            return False
    
    def add_watcher(self, callback):
        """添加配置变化监听器
        
        Args:
            callback: 回调函数，当配置变化时调用
        """
        self._watchers.append(callback)
    
    def remove_watcher(self, callback):
        """移除配置变化监听器
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self._watchers:
            self._watchers.remove(callback)
    
    def _notify_watchers(self):
        """通知所有监听器"""
        for callback in self._watchers:
            try:
                callback(self.config)
            except Exception as e:
                self.logger.error(f"配置监听器回调失败: {e}")
    
    def reload(self) -> bool:
        """重新加载配置
        
        Returns:
            bool: 重新加载是否成功
        """
        return self.load_config()
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config = SystemConfig()
        self.logger.info("配置已重置为默认值")
    
    def export_config(self, output_file: str, format: str = 'yaml') -> bool:
        """导出配置到文件
        
        Args:
            output_file: 输出文件路径
            format: 输出格式 ('yaml', 'json', 'toml')
            
        Returns:
            bool: 导出是否成功
        """
        try:
            config_dict = asdict(self.config)
            
            if format.lower() == 'yaml':
                if not YAML_AVAILABLE:
                    raise ImportError("PyYAML未安装")
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            
            elif format.lower() == 'json':
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            elif format.lower() == 'toml':
                if not TOML_AVAILABLE:
                    raise ImportError("toml未安装")
                with open(output_file, 'w', encoding='utf-8') as f:
                    toml.dump(config_dict, f)
            
            else:
                raise ValueError(f"不支持的格式: {format}")
            
            self.logger.info(f"配置已导出到: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def load_settings(self) -> Optional[Dict[str, Any]]:
        """加载设置
        
        Returns:
            Optional[Dict[str, Any]]: 设置字典，如果失败返回None
        """
        try:
            # 尝试从配置文件加载设置
            if self.config_file and os.path.exists(self.config_file):
                config_dict = asdict(self.config)
                return config_dict
            else:
                # 如果没有配置文件，返回默认配置
                return asdict(SystemConfig())
                
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
            return None
    
    def save_settings(self, settings: Dict[str, Any]) -> bool:
        """保存设置
        
        Args:
            settings: 设置字典
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 更新配置对象
            self._update_config_from_dict(settings)
            
            # 保存到文件
            return self.save_config()
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            return False


# 全局配置管理器实例
_config_manager = None
_config_lock = threading.Lock()


def get_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """获取配置管理器实例（单例）
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _config_manager
    
    if _config_manager is None:
        with _config_lock:
            if _config_manager is None:
                _config_manager = ConfigManager(config_file)
    
    return _config_manager


def get_config() -> SystemConfig:
    """获取系统配置
    
    Returns:
        SystemConfig: 系统配置对象
    """
    return get_config_manager().get_config()


def get_setting(key: str, default: Any = None) -> Any:
    """获取配置设置
    
    Args:
        key: 配置键
        default: 默认值
        
    Returns:
        配置值
    """
    return get_config_manager().get(key, default)


def set_setting(key: str, value: Any) -> bool:
    """设置配置值
    
    Args:
        key: 配置键
        value: 配置值
        
    Returns:
        bool: 设置是否成功
    """
    return get_config_manager().set(key, value)