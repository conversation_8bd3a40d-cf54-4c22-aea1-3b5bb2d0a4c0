"""
配置管理器单元测试
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

from config.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器测试类"""

    def test_init_default_config(self):
        """测试默认配置初始化"""
        config_manager = ConfigManager()
        assert config_manager is not None
        assert hasattr(config_manager, 'config')

    def test_load_config_from_file(self, temp_dir):
        """测试从文件加载配置"""
        config_data = {
            'database': {'url': 'sqlite:///test.db'},
            'features': {'cache_dir': '/tmp/cache'},
            'search': {'top_k': 10}
        }
        
        config_file = Path(temp_dir) / 'test_config.yaml'
        with open(config_file, 'w') as f:
            yaml.dump(config_data, f)
        
        config_manager = ConfigManager(config_file=str(config_file))
        assert config_manager.get_config('database.url') == 'sqlite:///test.db'
        assert config_manager.get_config('features.cache_dir') == '/tmp/cache'
        assert config_manager.get_config('search.top_k') == 10

    def test_get_config_with_default(self):
        """测试获取配置值（带默认值）"""
        config_manager = ConfigManager()
        value = config_manager.get_config('nonexistent.key', default='default_value')
        assert value == 'default_value'

    def test_set_config(self):
        """测试设置配置值"""
        config_manager = ConfigManager()
        config_manager.set_config('test.key', 'test_value')
        assert config_manager.get_config('test.key') == 'test_value'

    def test_get_nested_config(self):
        """测试获取嵌套配置"""
        config_data = {
            'database': {
                'connection': {
                    'host': 'localhost',
                    'port': 5432
                }
            }
        }
        
        with patch('builtins.open', mock_open(read_data=yaml.dump(config_data))):
            config_manager = ConfigManager()
            assert config_manager.get_config('database.connection.host') == 'localhost'
            assert config_manager.get_config('database.connection.port') == 5432

    def test_save_config(self, temp_dir):
        """测试保存配置"""
        config_file = Path(temp_dir) / 'save_test.yaml'
        config_manager = ConfigManager(config_file=str(config_file))
        
        config_manager.set_config('test.key', 'test_value')
        config_manager.save_config()
        
        # 验证文件是否被创建
        assert config_file.exists()

    def test_validate_config(self):
        """测试配置验证"""
        config_manager = ConfigManager()
        
        # 测试有效配置
        valid_config = {
            'database': {'url': 'sqlite:///test.db'},
            'features': {'cache_dir': '/tmp/cache'}
        }
        assert config_manager.validate_config(valid_config) is True
        
        # 测试无效配置
        invalid_config = {'invalid_key': 'value'}
        assert config_manager.validate_config(invalid_config) is False

    def test_merge_config(self):
        """测试配置合并"""
        config_manager = ConfigManager()
        
        base_config = {'key1': 'value1', 'key2': 'value2'}
        override_config = {'key2': 'new_value2', 'key3': 'value3'}
        
        merged = config_manager.merge_config(base_config, override_config)
        assert merged['key1'] == 'value1'
        assert merged['key2'] == 'new_value2'
        assert merged['key3'] == 'value3'

    def test_config_reload(self, temp_dir):
        """测试配置重载"""
        config_file = Path(temp_dir) / 'reload_test.yaml'
        
        # 初始配置
        initial_config = {'test': 'initial_value'}
        with open(config_file, 'w') as f:
            yaml.dump(initial_config, f)
        
        config_manager = ConfigManager(config_file=str(config_file))
        assert config_manager.get_config('test') == 'initial_value'
        
        # 更新配置文件
        updated_config = {'test': 'updated_value'}
        with open(config_file, 'w') as f:
            yaml.dump(updated_config, f)
        
        config_manager.reload_config()
        assert config_manager.get_config('test') == 'updated_value'

    def test_config_export(self, temp_dir):
        """测试配置导出"""
        config_manager = ConfigManager()
        config_manager.set_config('export.test', 'export_value')
        
        export_file = Path(temp_dir) / 'exported_config.yaml'
        config_manager.export_config(str(export_file))
        
        assert export_file.exists()
        
        # 验证导出内容
        with open(export_file, 'r') as f:
            exported_data = yaml.safe_load(f)
        assert exported_data['export']['test'] == 'export_value' 