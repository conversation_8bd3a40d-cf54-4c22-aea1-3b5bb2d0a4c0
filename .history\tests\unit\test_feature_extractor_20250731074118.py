"""
特征提取器单元测试
"""

import numpy as np
from PIL import Image
from unittest.mock import Mock, patch
import pytest

from features.core.feature_extractor import FeatureExtractor
from features.extractors.color_extractor import ColorExtractor
from features.extractors.texture_extractor import TextureExtractor
from features.extractors.shape_extractor import ShapeExtractor


class TestFeatureExtractor:
    """特征提取器测试类"""

    def test_init(self):
        """测试初始化"""
        extractor = FeatureExtractor()
        assert extractor is not None
        assert hasattr(extractor, 'extractors')

    def test_register_extractor(self):
        """测试注册提取器"""
        extractor = FeatureExtractor()
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        
        extractor.register_extractor(mock_extractor)
        assert 'test_extractor' in extractor.extractors

    def test_extract_features(self, test_images_dir):
        """测试特征提取"""
        extractor = FeatureExtractor()
        
        # 创建测试图像
        test_image_path = f"{test_images_dir}/test_image_0.jpg"
        
        with patch.object(extractor, 'extractors', {
            'color': Mock(return_value={'color': [0.1, 0.2, 0.3]}),
            'texture': Mock(return_value={'texture': [0.4, 0.5, 0.6]}),
            'shape': Mock(return_value={'shape': [0.7, 0.8, 0.9]})
        }):
            features = extractor.extract_features(test_image_path)
            
            assert 'color' in features
            assert 'texture' in features
            assert 'shape' in features

    def test_extract_features_with_invalid_image(self):
        """测试无效图像的特征提取"""
        extractor = FeatureExtractor()
        
        with pytest.raises(ValueError):
            extractor.extract_features('/nonexistent/image.jpg')

    def test_batch_extract_features(self, test_images_dir):
        """测试批量特征提取"""
        extractor = FeatureExtractor()
        
        # 创建多个测试图像路径
        image_paths = [
            f"{test_images_dir}/test_image_0.jpg",
            f"{test_images_dir}/test_image_1.jpg"
        ]
        
        with patch.object(extractor, 'extract_features') as mock_extract:
            mock_extract.return_value = {
                'color': [0.1, 0.2, 0.3],
                'texture': [0.4, 0.5, 0.6]
            }
            
            results = extractor.batch_extract_features(image_paths)
            
            assert len(results) == 2
            assert mock_extract.call_count == 2

    def test_get_extractor_info(self):
        """测试获取提取器信息"""
        extractor = FeatureExtractor()
        
        # 注册测试提取器
        mock_extractor = Mock()
        mock_extractor.name = 'test_extractor'
        mock_extractor.version = '1.0.0'
        mock_extractor.description = 'Test extractor'
        
        extractor.register_extractor(mock_extractor)
        
        info = extractor.get_extractor_info()
        assert 'test_extractor' in info
        assert info['test_extractor']['version'] == '1.0.0'


class TestColorExtractor:
    """颜色特征提取器测试类"""

    def test_init(self):
        """测试初始化"""
        extractor = ColorExtractor()
        assert extractor is not None
        assert extractor.name == 'color'

    def test_extract_color_histogram(self):
        """测试颜色直方图提取"""
        extractor = ColorExtractor()
        
        # 创建测试图像
        img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        histogram = extractor.extract_color_histogram(img)
        assert len(histogram) > 0
        assert all(isinstance(x, (int, float)) for x in histogram)

    def test_extract_dominant_colors(self):
        """测试主色调提取"""
        extractor = ColorExtractor()
        
        # 创建测试图像
        img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        colors = extractor.extract_dominant_colors(img, n_colors=5)
        assert len(colors) <= 5
        assert all(len(color) == 3 for color in colors)

    def test_extract_color_moments(self):
        """测试颜色矩提取"""
        extractor = ColorExtractor()
        
        # 创建测试图像
        img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        moments = extractor.extract_color_moments(img)
        assert len(moments) > 0
        assert all(isinstance(x, float) for x in moments)


class TestTextureExtractor:
    """纹理特征提取器测试类"""

    def test_init(self):
        """测试初始化"""
        extractor = TextureExtractor()
        assert extractor is not None
        assert extractor.name == 'texture'

    def test_extract_glcm_features(self):
        """测试GLCM特征提取"""
        extractor = TextureExtractor()
        
        # 创建测试图像
        img_array = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        
        features = extractor.extract_glcm_features(img_array)
        assert len(features) > 0
        assert all(isinstance(x, float) for x in features.values())

    def test_extract_lbp_features(self):
        """测试LBP特征提取"""
        extractor = TextureExtractor()
        
        # 创建测试图像
        img_array = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        
        features = extractor.extract_lbp_features(img_array)
        assert len(features) > 0
        assert all(isinstance(x, (int, float)) for x in features)

    def test_extract_gabor_features(self):
        """测试Gabor特征提取"""
        extractor = TextureExtractor()
        
        # 创建测试图像
        img_array = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        
        features = extractor.extract_gabor_features(img_array)
        assert len(features) > 0
        assert all(isinstance(x, float) for x in features)


class TestShapeExtractor:
    """形状特征提取器测试类"""

    def test_init(self):
        """测试初始化"""
        extractor = ShapeExtractor()
        assert extractor is not None
        assert extractor.name == 'shape'

    def test_extract_contour_features(self):
        """测试轮廓特征提取"""
        extractor = ShapeExtractor()
        
        # 创建测试图像（简单形状）
        img_array = np.zeros((100, 100), dtype=np.uint8)
        img_array[25:75, 25:75] = 255  # 创建矩形
        
        features = extractor.extract_contour_features(img_array)
        assert len(features) > 0
        assert all(isinstance(x, float) for x in features.values())

    def test_extract_moments(self):
        """测试矩特征提取"""
        extractor = ShapeExtractor()
        
        # 创建测试图像
        img_array = np.zeros((100, 100), dtype=np.uint8)
        img_array[25:75, 25:75] = 255
        
        moments = extractor.extract_moments(img_array)
        assert len(moments) > 0
        assert all(isinstance(x, float) for x in moments)

    def test_extract_hu_moments(self):
        """测试Hu矩特征提取"""
        extractor = ShapeExtractor()
        
        # 创建测试图像
        img_array = np.zeros((100, 100), dtype=np.uint8)
        img_array[25:75, 25:75] = 255
        
        hu_moments = extractor.extract_hu_moments(img_array)
        assert len(hu_moments) == 7
        assert all(isinstance(x, float) for x in hu_moments) 